# Silent Production Deployment Checklist

## Overview
This checklist ensures a silent deployment of the multilingual infrastructure without exposing the language switcher to users until ready.

## ✅ Pre-Deployment Changes Made

### **1. Language Switcher Hidden**
- **Desktop switcher**: Commented out `@include('components.language-switcher')` in `resources/views/components/header.blade.php`
- **Mobile switcher**: Commented out mobile language switching menu in header
- **Test route**: Commented out `test-language-switch` route in `routes/web.php`

### **2. Infrastructure Ready**
- **English language files**: All 28 files created and translated
- **Helper functions**: Language switching functions implemented but hidden
- **Middleware**: Fallback middleware active for graceful handling
- **Locale detection**: Working for staging/local environments

### **3. Backend Features Active**
- **People untranslated link**: Added to Uranus admin interface
- **Console commands**: Available for translation management
- **Translation infrastructure**: Fully functional but not user-facing

## ✅ What's Deployed Silently

### **Invisible to Users**
1. **Language switching infrastructure** (hidden UI)
2. **English language files** (available but not accessible)
3. **URL routing** (`/en/` prefix works but no links to it)
4. **Translation management tools** (admin-only)

### **Visible to Admins Only**
1. **Uranus people index**: Shows untranslated people count
2. **Console commands**: For translation management
3. **Translation edit pages**: For content translation work

## ✅ Production Environment Setup

### **Environment Variables**
```bash
# In production .env
APP_ENV=production
APP_DEBUG=false
DEFAULT_LOCALE=el

# Locale detection will be disabled in production
# (only works in local/staging environments)
```

### **Database**
- **Translation tables**: Ready (`people_translations`, `theatric_plays_translations`, etc.)
- **Existing content**: Remains unchanged
- **New translations**: Can be added via admin interface

## ✅ Testing Performed

### **Syntax Checks**
- ✅ Header blade template syntax verified
- ✅ Routes file syntax verified
- ✅ Helper functions syntax verified

### **Functionality Checks**
- ✅ Language switcher properly hidden
- ✅ No JavaScript references to language switching
- ✅ Middleware properly configured
- ✅ English language files in place
- ✅ Admin interface enhancements working

### **Security Checks**
- ✅ No test routes exposed
- ✅ No debug functionality in production
- ✅ Locale detection disabled in production environment

## ✅ Post-Deployment Verification

### **User-Facing (Should be unchanged)**
1. **Homepage**: No language switcher visible
2. **Navigation**: Standard Greek navigation only
3. **URLs**: Only Greek URLs accessible to users
4. **Content**: All content in Greek as before

### **Admin-Facing (New features)**
1. **People index**: Shows untranslated count link
2. **Translation management**: Available for content work
3. **Console commands**: Working for analytics

### **URL Testing**
```bash
# These should work (but not linked from UI)
https://unstage.gr/en/  # English homepage (if manually accessed)
https://unstage.gr/en/person/john-doe  # English person page (if exists)

# These should redirect gracefully
https://unstage.gr/en/non-existent-page  # Redirect to Greek homepage with message
```

## ✅ Activation Plan (Future)

When ready to activate language switching:

### **Step 1: Enable UI**
```php
// In resources/views/components/header.blade.php
@include('components.language-switcher')  // Uncomment this line

// Remove comment blocks around mobile language switching
```

### **Step 2: Enable in Production**
```php
// In app/Components/helpers.php - modify setUnstageLocale()
// Change from: ['local', 'staging']
// To: ['local', 'staging', 'production']
```

### **Step 3: Add Content**
- Translate key pages via admin interface
- Test language switching functionality
- Monitor for any issues

## ✅ Rollback Plan

If any issues arise:

### **Immediate Rollback**
1. **Re-comment language switcher** if accidentally enabled
2. **Disable middleware** by removing from Kernel.php
3. **Revert locale detection** to debug-only mode

### **Files to Monitor**
- `resources/views/components/header.blade.php`
- `app/Http/Kernel.php`
- `app/Components/helpers.php`
- `routes/web.php`

## ✅ Benefits of Silent Deployment

1. **Zero User Impact**: Users see no changes
2. **Infrastructure Ready**: All backend systems prepared
3. **Admin Tools Available**: Translation work can begin immediately
4. **Gradual Activation**: Can enable features when ready
5. **Risk Mitigation**: No user-facing changes until tested

## ✅ Next Steps After Deployment

### **Immediate (Admin Tasks)**
1. **Check people untranslated count** in Uranus
2. **Run console commands** to verify translation analytics
3. **Begin translation work** on high-priority content

### **Short Term (Content Preparation)**
1. **Translate key pages** (homepage, about, contact)
2. **Translate popular people profiles**
3. **Translate current plays**

### **Medium Term (Activation)**
1. **Enable language switcher** when content ready
2. **Test user experience** thoroughly
3. **Monitor analytics** for language usage

## ✅ Monitoring Points

### **Error Monitoring**
- Watch for any 404s on `/en/` URLs
- Monitor fallback middleware performance
- Check for any translation-related errors

### **Performance Monitoring**
- Ensure no performance impact from hidden features
- Monitor database queries for translation tables
- Check memory usage with new language files

## ✅ Success Criteria

### **Deployment Success**
- ✅ No user-visible changes
- ✅ No errors in logs
- ✅ Admin features working
- ✅ Infrastructure ready for activation

### **Ready for Activation**
- [ ] Key content translated
- [ ] User testing completed
- [ ] Performance verified
- [ ] Language switcher enabled

---

**Status**: Ready for silent production deployment
**Risk Level**: Low (no user-facing changes)
**Rollback Time**: < 5 minutes if needed
