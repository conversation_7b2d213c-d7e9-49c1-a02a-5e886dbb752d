<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

class MigrateSchoolsDataToTranslations extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Migrate existing data to Greek translations
        DB::statement('
            INSERT INTO schools_translations (school_id, locale, name, description, created_at, updated_at)
            SELECT 
                id,
                "el" as locale,
                name,
                description,
                created_at,
                updated_at
            FROM schools
            WHERE name IS NOT NULL AND name != ""
        ');
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // Remove Greek translations and restore data to main table
        DB::statement('
            UPDATE schools 
            SET 
                name = (
                    SELECT name 
                    FROM schools_translations 
                    WHERE schools_translations.school_id = schools.id 
                    AND schools_translations.locale = "el" 
                    LIMIT 1
                ),
                description = (
                    SELECT description 
                    FROM schools_translations 
                    WHERE schools_translations.school_id = schools.id 
                    AND schools_translations.locale = "el" 
                    LIMIT 1
                )
        ');
        
        // Delete all translations
        DB::table('schools_translations')->delete();
    }
}
