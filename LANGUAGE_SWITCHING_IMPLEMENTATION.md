# Language Switching Implementation - Step 1.1

## Overview
This document outlines the implementation of Step 1.1: Environment Configuration & Language Switching Behavior for the unstage platform.

## What Was Implemented

### 1. Environment Configuration
- **Updated `.env`**: Added `DEFAULT_LOCALE=el` configuration
- **Modified locale detection**: Updated `setUnstageLocale()` function in `app/Components/helpers.php` to work in both local and staging environments (not just debug mode)

### 2. Language Switching Helper Functions
Added the following helper functions to `app/Components/helpers.php`:

#### `localizedRoute($name, $parameters = [], $locale = null)`
- Generates localized route URLs
- Handles default locale (no prefix) vs non-default locale (with prefix)

#### `switchLanguageUrl($targetLocale)`
- **Core functionality**: Generates URL for switching to a different language while maintaining the current page
- **Equivalent Page Switching**: Preserves the current page structure when switching languages
- **Examples**:
  - `/person/john-doe` → `/en/person/john-doe` (Greek to English)
  - `/en/play/hamlet-2024` → `/play/hamlet-2024` (English to Greek)
  - `/en/theatre/national-theatre` → `/theatre/national-theatre` (English to Greek)

#### `getAlternateLanguageUrls()`
- Returns URLs for all available languages for the current page
- Useful for generating hreflang tags and language switcher menus

#### `getCurrentPageInLanguage($targetLocale)`
- Placeholder function to check if current page exists in target language
- Currently returns true for all pages (can be enhanced later)

### 3. Language Switcher UI Component
Created `resources/views/components/language-switcher.blade.php`:
- **Desktop version**: Dropdown with globe icon and language names
- **Mobile version**: Integrated into mobile hamburger menu
- **Styling**: Added CSS in `public/css/frontend/main.css`
- **Placement**: Added to header navigation in `resources/views/components/header.blade.php`

### 4. Fallback Behavior Implementation
Created `app/Http/Middleware/LanguageSwitchFallback.php`:
- **Handles 404s on English pages**: If English translation doesn't exist, redirects to English homepage
- **User notification**: Shows message "This content is not yet available in English"
- **Content route detection**: Identifies content pages that might not have translations
- **Registered in Kernel**: Added to web middleware group

### 5. Flash Message Enhancement
Updated `resources/views/components/messages/flash.blade.php`:
- Added support for language fallback notifications
- Displays globe icon with fallback message

## URL Structure Examples

### Current Behavior (Implemented)
- **Greek (default)**: `https://unstage.dev/person/john-doe`
- **English**: `https://unstage.dev/en/person/john-doe`

### Language Switching Examples
- From `/person/john-doe` → Click English → `/en/person/john-doe`
- From `/en/play/hamlet-2024` → Click Greek → `/play/hamlet-2024`
- From `/en/theatre/national-theatre` → Click Greek → `/theatre/national-theatre`

## Fallback Behavior

### Option A: Equivalent Page Switching (Implemented)
When user switches language:
1. **If translation exists**: Redirect to equivalent page in target language
2. **If translation doesn't exist**: Redirect to target language homepage with notification
3. **Query parameters**: Preserved when possible
4. **Content routes affected**: plays.show, people.show, theatres.show, movies.show, tvshows.show, news.show, interviews.show

## Files Modified/Created

### New Files
- `resources/views/components/language-switcher.blade.php`
- `app/Http/Middleware/LanguageSwitchFallback.php`
- `LANGUAGE_SWITCHING_IMPLEMENTATION.md`

### Modified Files
- `.env` - Added DEFAULT_LOCALE configuration
- `app/Components/helpers.php` - Added language switching helper functions
- `resources/views/components/header.blade.php` - Added language switcher to navigation
- `public/css/frontend/main.css` - Added language switcher styling
- `app/Http/Kernel.php` - Registered fallback middleware
- `resources/views/components/messages/flash.blade.php` - Added fallback message support
- `routes/web.php` - Added test route for language switching

## Testing

### Test Route (Local Environment Only)
Visit `/test-language-switch` to see:
- Current locale
- Generated switch URLs
- All alternate language URLs

### Manual Testing
1. **Language Switcher**: Check header for globe icon dropdown
2. **URL Generation**: Switch between languages and verify URL structure
3. **Fallback Behavior**: Try accessing non-existent English content
4. **Mobile Menu**: Check language options in mobile hamburger menu

## Next Steps

### Immediate
1. **Test in staging environment** with English locale enabled
2. **Verify URL routing** with `/en/` prefix works correctly
3. **Test language switching** on various page types

### Future Enhancements
1. **Content existence checking**: Enhance `getCurrentPageInLanguage()` to check actual translation availability
2. **SEO optimization**: Add hreflang tags using `getAlternateLanguageUrls()`
3. **Search functionality**: Update search to work with English content
4. **Performance optimization**: Cache language switching URLs

## Configuration Notes

### Environment Variables
- `DEFAULT_LOCALE=el` - Sets Greek as default language
- `APP_ENV=staging` - Enables locale switching in staging environment
- `APP_DEBUG=true` - Enables locale switching in debug mode

### Locale Configuration
- **Default locale**: `el` (Greek) - no URL prefix
- **Allowed locales**: `['el', 'en']` - defined in `config/locale.php`
- **URL structure**: Default locale has no prefix, others have language prefix

## Rollback Plan
If issues arise:
1. **Hide language switcher**: Comment out `@include('components.language-switcher')` in header
2. **Disable middleware**: Remove `LanguageSwitchFallback` from Kernel
3. **Revert locale detection**: Change `setUnstageLocale()` back to debug-only mode
4. **Remove test routes**: Clean up test routes from `routes/web.php`
