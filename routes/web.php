<?php

use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/


// =======================================================================//
//                               Test routes
// =======================================================================//
//Route::get('testemail', function () {
//    return view('hermes::neptune.welcome_new_user');
//});
//Route::get('testevent', 'TestController@event');
//Route::get('testrelation', 'TestController@testRelation');
Route::get('sendwelcome/{id}', 'TestController@sendWelcome');

// =======================================================================//
//                               On Demand
// =======================================================================//
Route::get('streams', 'OnDemandController@home')->name('onDemand.home');
Route::get('streams/title/{slug}', 'OnDemandController@show')->name('onDemand.show');
Route::get('streams/t/{slug}', 'OnDemandController@showOld')->name('onDemand.showOld');
Route::get('streams/browse', 'OnDemandController@index')->name('onDemand.index');

// =======================================================================//
//                               Search
// =======================================================================//
Route::post('suggest', 'SearchController@suggest')->name('suggest');
Route::get('anazitisi', 'SearchController@search')->name('search');
Route::get('anazitisi/sintelestes', 'SearchController@people')->name('people.search');
Route::get('anazitisi/theatra', 'SearchController@theatres')->name('theatres.search');
Route::get('anazitisi/parastaseis', 'SearchController@plays')->name('plays.search');
Route::get('anazitisi/sxoles', 'SearchController@schools')->name('dramaSchools.search');
Route::get('anazitisi/tainies', 'SearchController@movies')->name('movies.search');
Route::get('anazitisi/seires', 'SearchController@tvShows')->name('tvShows.search');
Route::get('anazitisi/ponimata', 'SearchController@endeavours')->name('endeavours.search');

// =======================================================================//
//                               Home
// =======================================================================//
Route::get('/', 'HomepageController@index')->name('homepage');

// =======================================================================//
//                               People
// =======================================================================//
Route::get('sintelestes/{slug}/bio', 'PeopleController@showBio')->name('people.showBio');
Route::get('sintelestes/{slug}/media', 'PeopleController@showMedia')->name('people.showMedia');
Route::get('sintelestes/{slug}', 'PeopleController@show')->name('people.show');
Route::get('sintelestes', 'PeopleController@index')->name('people.index');

// =======================================================================//
//                               Theatres
// =======================================================================//
Route::get('theatra/{slug}/plays', 'TheatresController@findPlays')->name('theatres.findPlays');
Route::get('theatra/{slug}', 'TheatresController@show')->name('theatres.show');

// =======================================================================//
//                            Drama Schools
// =======================================================================//
Route::get('dramatikes-sxoles/{slug}/apofoitoi', 'DramaSchoolsController@showAlumni')->name('dramaSchools.showAlumni');
//Route::get('dramatikes-sxoles/{slug}', 'DramaSchoolsController@show')->name('dramaSchools.show');
Route::get('dramatikes-sxoles', 'DramaSchoolsController@index')->name('dramaSchools.index');

// =======================================================================//
//                               Festivals
// =======================================================================//
Route::get('festival/{slug}', 'FestivalsController@show')->name('festivals.show');
Route::get('festival', 'FestivalsController@index')->name('festivals.index');

// =======================================================================//
//                               Plays / Trailers
// =======================================================================//
Route::get('parastaseis/trailers', 'VideosController@indexPlayTrailers')->name('plays.trailers.index');

Route::get('parastaseis/{slug}/media', 'PlaysController@showMedia')->name('plays.showMedia');
Route::get('parastaseis/{slug}/info', 'PlaysController@showInfo')->name('plays.showInfo');
Route::get('parastaseis/{slug}/kritikes-xriston', 'PlaysController@showUserReviews')->name('plays.showUserReviews');
Route::get('parastaseis/{slug}/kritikes', 'PlaysController@showCriticReviews')->name('plays.showCriticReviews');
Route::get('parastaseis/{slug}', 'PlaysController@show')->name('plays.show');
Route::get('parastaseis', 'PlaysController@index')->name('plays.index');

// =======================================================================//
//                               Movies
// =======================================================================//
Route::get('cinema/{slug}/media', 'MoviesController@showMedia')->name('movies.showMedia');
Route::get('cinema/{slug}/info', 'MoviesController@showInfo')->name('movies.showInfo');
Route::get('cinema/{slug}/kritikes-koinou', 'MoviesController@showUserReviews')->name('movies.showUserReviews');
Route::get('cinema/{slug}', 'MoviesController@show')->name('movies.show');

// =======================================================================//
//                               Endeavours
// =======================================================================//
Route::get('e/{slug}/media', 'EndeavoursController@showMedia')->name('endeavours.showMedia');
Route::get('e/{slug}/info', 'EndeavoursController@showInfo')->name('endeavours.showInfo');
//Route::get('e/{slug}/kritikes-koinou', 'MoviesController@showUserReviews')->name('movies.showUserReviews');
Route::get('e/{slug}', 'EndeavoursController@show')->name('endeavours.show');

// =======================================================================//
//                               Videos
// =======================================================================//
Route::get('videos', 'VideosController@index')->name('videos.index');

// =======================================================================//
//                               Tv Shows
// =======================================================================//
Route::get('tv/{slug}/media', 'TvShowsController@showMedia')->name('tvShows.showMedia');
Route::get('tv/{slug}/info', 'TvShowsController@showInfo')->name('tvShows.showInfo');
Route::get('tv/{slug}/kritikes-koinou', 'TvShowsController@showUserReviews')->name('tvShows.showUserReviews');
Route::get('tv/{slug}', 'TvShowsController@show')->name('tvShows.show');

// =======================================================================//
//                               Plays / Referral Click log
// =======================================================================//
Route::post('referralClick', 'PlaysController@referralClick')->name('plays.referralClick');

// =======================================================================//
//                             Magazine
// =======================================================================//
Route::get('magazine/sinenteukseis', 'ArticlesController@interviewsIndex')->name('interviews.index');
Route::get('magazine/news', 'ArticlesController@newsIndex')->name('news.index');
Route::get('magazine/{slug}', 'ArticlesController@show')->name('articles.show');

// =======================================================================//
//                       Enduser pages
// =======================================================================//
Route::get('magazine/u/{slug}', 'UsersController@show')->name('users.show');

// =======================================================================//
//                               Visits
// =======================================================================//
// temporarily commented out, if no issue arises, we will delete the route (and the relevant controller) cleanly
//Route::post('visit/endTime', 'VisitsController@storeEndTime')->name('visit.storeEndTime');

// =======================================================================//
//                                RSS
// =======================================================================//
Route::feeds();

// =======================================================================//
//                        Contact / Sinergasies
// =======================================================================//
Route::get('epikoinonia', 'ContactController@showContact')->name('contact.show');
Route::post('epikoinonia', 'ContactController@handleContact')->name('contact.handle');
//Route::get('sinergasies', 'CollaborationsController@show')->name('collaboration.show');
Route::post('sinergasies', 'CollaborationsController@handle')->name('collaboration.handle');

// =======================================================================//
//                              Contributions
// =======================================================================//
Route::get('simvoli', 'ContributionsController@create')->name('contributions.create');
Route::post('simvoli', 'ContributionsController@store')->name('contributions.store');

// =======================================================================//
//                              About us et al.
// =======================================================================//
Route::get('sxetika-me-emas', 'AboutUsController@index')->name('about.index');
Route::get('terms', 'AboutUsController@indexTerms')->name('terms.index');


// =======================================================================//
//                              ADS
// =======================================================================//
Route::get('ads', 'AdsController@index')->name('ads.index');

// =======================================================================//
//                       Enduser register / login
// =======================================================================//
Route::get('join', '\Packages\Neptune\app\Http\Controllers\Auth\RegisterController@showRegistrationForm')->name('register.show');
Route::post('join', '\Packages\Neptune\app\Http\Controllers\Auth\RegisterController@register')->name('register.handle');
Route::get('login', '\Packages\Neptune\app\Http\Controllers\Auth\LoginController@showLoginForm')->name('login.show');
Route::post('login', '\Packages\Neptune\app\Http\Controllers\Auth\LoginController@login')->name('login.handle');
Route::get('logout', '\Packages\Neptune\app\Http\Controllers\Auth\LoginController@logout')->name('logout');

// =======================================================================//
//                             Social Authentication
// =======================================================================//
Route::get('login/{provider}', '\Packages\Neptune\app\Http\Controllers\Auth\SocialLoginController@redirectToProvider')
    ->name('neptune.redirect_to_provider')
    ->where('provider', (implode('|', config('auth.social.providers'))));
Route::get('login/{provider}/callback', '\Packages\Neptune\app\Http\Controllers\Auth\SocialLoginController@handleProviderCallback')
    ->name('neptune.handle_provider_callback');

// =======================================================================//
//                            Email Verification
// =======================================================================//
Route::get('email/verify/{token}', '\Packages\Neptune\app\Http\Controllers\Auth\VerifyEmailController@store')->name('neptune.verify_email');

// =======================================================================//
//                            Password Reset
// =======================================================================//
Route::get('password/reset', '\Packages\Neptune\app\Http\Controllers\Auth\ForgotPasswordController@showLinkRequestForm')->name('neptune.reset_password');
Route::post('password/email', '\Packages\Neptune\app\Http\Controllers\Auth\ForgotPasswordController@sendResetLinkEmail')->name('neptune.send_email_reset_link');
Route::get('password/reset/{token}', '\Packages\Neptune\app\Http\Controllers\Auth\ResetPasswordController@showResetForm')->name('neptune.new_password_form');
Route::post('password/change', '\Packages\Neptune\app\Http\Controllers\Auth\ResetPasswordController@reset')->name('neptune.post_reset_password');

// Only expose routes on local env
if(env('APP_ENV') === 'local')
{
    Route::get('test-g-translate', 'TestController@testGTranslate');
    Route::get('test-general', 'TestController@testGeneral');
    Route::get('test-parse-url', 'TestController@testParseUrl');
    Route::get('testTranslate', 'TestController@testTranslatePlay');
    Route::get('testTranslateQuery', 'TestController@testTranslatedQueries');
    Route::get('streamingRecommendation', 'TestController@streamingRecommendation');
    Route::get('testSQS', 'TestController@testSQS');
    Route::get('getEmailMessage', 'TestController@getEmailMessage');
    Route::view('/welcomepro', 'hermes::neptune.pro.welcome_pro_user');

    // Test language switching functionality
    // Route::get('test-language-switch', function() {
    //     return [
    //         'current_locale' => app()->getLocale(),
    //         'switch_to_en' => switchLanguageUrl('en'),
    //         'switch_to_el' => switchLanguageUrl('el'),
    //         'alternate_urls' => getAlternateLanguageUrls(),
    //     ];
    // });
}
