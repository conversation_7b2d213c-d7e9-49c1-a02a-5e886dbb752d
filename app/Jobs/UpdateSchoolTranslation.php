<?php

namespace App\Jobs;

use App\Models\School;
use Aws\Exception\AwsException;
use Aws\Translate\TranslateClient;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;

class UpdateSchoolTranslation implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $school;
    protected $translationClient;
    protected $sourceLang = 'el';
    protected $targetLang = 'en';

    /**
     * Create a new job instance.
     *
     * @param School $school
     */
    public function __construct(School $school)
    {
        $this->school = $school;
        
        $this->translationClient = new TranslateClient([
            'version' => 'latest',
            'region'  => config('aws.region'),
            'credentials' => [
                'key'    => config('aws.credentials.key'),
                'secret' => config('aws.credentials.secret'),
            ],
        ]);
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        // Skip if already has English translation
        if ($this->school->hasTranslation($this->targetLang)) {
            return;
        }

        try {
            // Translate name
            if ($this->school->name) {
                $nameTranslation = $this->translateText($this->school->name);
                if ($nameTranslation) {
                    $this->school->translateOrNew($this->targetLang)->name = $nameTranslation;
                }
            }

            // Translate description
            if ($this->school->description) {
                // Strip HTML tags for translation
                $cleanDescription = strip_tags($this->school->description);
                if (!empty($cleanDescription) && mb_strlen($cleanDescription) < 2000) {
                    $descriptionTranslation = $this->translateText($cleanDescription);
                    if ($descriptionTranslation) {
                        $this->school->translateOrNew($this->targetLang)->description = $descriptionTranslation;
                    }
                }
            }

            // Save the translations
            $this->school->save();

        } catch (AwsException $e) {
            // Log the error but don't fail the job
            \Log::error('School translation failed for ID ' . $this->school->id . ': ' . $e->getMessage());
        }
    }

    /**
     * Translate text using AWS Translate
     *
     * @param string $text
     * @return string|null
     */
    private function translateText($text)
    {
        if (empty($text) || mb_strlen($text) >= 2000) {
            return null;
        }

        try {
            $result = $this->translationClient->translateText([
                'SourceLanguageCode' => $this->sourceLang,
                'TargetLanguageCode' => $this->targetLang,
                'Text' => $text,
            ]);

            return $result->get('TranslatedText');

        } catch (AwsException $e) {
            \Log::error('AWS Translate error: ' . $e->getMessage());
            return null;
        }
    }
}
