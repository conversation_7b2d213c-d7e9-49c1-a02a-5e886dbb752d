<?php

namespace App\Providers;

use Illuminate\Cache\RateLimiting\Limit;
use Illuminate\Foundation\Support\Providers\RouteServiceProvider as ServiceProvider;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\Facades\Route;

class RouteServiceProvider extends ServiceProvider
{
    /**
     * The path to the "home" route for your application.
     *
     * Typically, users are redirected here after authentication.
     *
     * @var string
     */
    public const HOME = '/';

    /**
     * The controller namespace for the application.
     *
     * When present, controller route declarations will automatically be prefixed with this namespace.
     *
     * @var string|null
     */
     protected $namespace = 'App\\Http\\Controllers';

    /**
     * Define your route model bindings, pattern filters, and other route configuration.
     */
    public function boot(): void
    {
        $this->configureRateLimiting();
        $this->configureRoutePatterns();

        $this->routes(function () {
            // API routes (no localization)
            Route::middleware('api')
                ->prefix('api')
                ->group(base_path('routes/api.php'));

            // Localized web routes
            $this->registerLocalizedRoutes();
        });
    }

    /**
     * Configure route patterns for locale detection.
     */
    protected function configureRoutePatterns(): void
    {
        // Define locale pattern for route parameters
        Route::pattern('locale', '[a-z]{2}');
    }

    /**
     * Register localized web routes.
     */
    protected function registerLocalizedRoutes(): void
    {
        $allowedLocales = config('locale.allowed_locales', []);
        $defaultLocale = config('locale.default_locale');

        // Routes for non-default locales (with prefix)
        foreach ($allowedLocales as $locale) {
            if ($locale !== $defaultLocale) {
                Route::middleware(['web', 'setlocale'])
                    ->prefix($locale)
                    ->name($locale . '.')
                    ->namespace($this->namespace)
                    ->group(base_path('routes/web.php'));
            }
        }

        // Routes for default locale (no prefix)
        Route::middleware(['web', 'setlocale'])
            ->namespace($this->namespace)
            ->group(base_path('routes/web.php'));
    }

    /**
     * Configure the rate limiters for the application.
     */
    protected function configureRateLimiting(): void
    {
        RateLimiter::for('api', function (Request $request) {
            return Limit::perMinute(60)->by($request->user()?->id ?: $request->ip());
        });
    }
}
