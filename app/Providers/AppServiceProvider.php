<?php

namespace App\Providers;

use App\Components\Request;
use App\Filters\PlayFilters;
use Illuminate\Pagination\Paginator;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        $this->app->singleton(PlayFilters::class, function ($app) {
            return new PlayFilters($app->make('request'));
        });

        // Register localization helper as singleton
        $this->app->singleton('localization', \App\Helpers\LocalizationHelper::class);
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // Bootstrap paginator
        Paginator::useBootstrap();

        Schema::defaultStringLength(191);

        $environment = $this->app->environment();

        if (extension_loaded('newrelic') && $environment == 'production') {
            if (Request::isCMS()) {
                newrelic_set_appname('Uranus');
            }
            else {
                newrelic_set_appname('Unstage');
            }
        }
    }
}
