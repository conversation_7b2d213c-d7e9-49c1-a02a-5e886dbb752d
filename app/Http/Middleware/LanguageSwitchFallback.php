<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class LanguageSwitchFallback
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        $response = $next($request);
        
        // Check if we're on an English page and got a 404
        if ($response->getStatusCode() === 404 && app()->getLocale() === 'en') {
            // Check if this is a content page that might not have English translation
            $routeName = $request->route() ? $request->route()->getName() : null;
            
            if ($this->isContentRoute($routeName)) {
                // Redirect to English homepage with notification
                return redirect()->route('homepage')
                    ->with('language_fallback_message', 
                        'This content is not yet available in English. You have been redirected to the homepage.');
            }
        }
        
        return $response;
    }
    
    /**
     * Check if the route is a content route that might not have translations.
     *
     * @param string|null $routeName
     * @return bool
     */
    private function isContentRoute($routeName)
    {
        $contentRoutes = [
            'plays.show',
            'people.show',
            'theatres.show',
            'movies.show',
            'tvshows.show',
            'news.show',
            'interviews.show',
        ];
        
        return in_array($routeName, $contentRoutes);
    }
}
