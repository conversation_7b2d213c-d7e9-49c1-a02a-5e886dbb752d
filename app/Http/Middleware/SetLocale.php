<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Session;
use Symfony\Component\HttpFoundation\Response;

class SetLocale
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Check if locale detection is enabled for current environment
        if (!$this->isLocaleDetectionEnabled()) {
            App::setLocale(config('locale.default_locale'));
            return $next($request);
        }

        // Get locale from URL segment
        $locale = $this->getLocaleFromRequest($request);

        // Validate and set locale
        if ($this->isValidLocale($locale)) {
            App::setLocale($locale);
            if (config('locale.enable_locale_session_storage', true)) {
                Session::put('locale', $locale);
            }
        } else {
            // Fallback to default locale
            $defaultLocale = config('locale.default_locale');
            App::setLocale($defaultLocale);
        }

        return $next($request);
    }

    /**
     * Extract locale from request URL.
     */
    private function getLocaleFromRequest(Request $request): string
    {
        $segments = $request->segments();
        $firstSegment = $segments[0] ?? '';

        // Check if first segment is a valid locale
        if ($this->isValidLocale($firstSegment)) {
            return $firstSegment;
        }

        // Fallback to session, browser preference, or default
        return $this->getPreferredLocale($request);
    }

    /**
     * Get preferred locale from various sources.
     */
    private function getPreferredLocale(Request $request): string
    {
        // 1. Check session
        if (Session::has('locale') && $this->isValidLocale(Session::get('locale'))) {
            return Session::get('locale');
        }

        // 2. Check browser Accept-Language header (optional)
        if (config('locale.detect_browser_locale', false)) {
            $browserLocale = $this->getBrowserLocale($request);
            if ($browserLocale && $this->isValidLocale($browserLocale)) {
                return $browserLocale;
            }
        }

        // 3. Default locale
        return config('locale.default_locale');
    }

    /**
     * Extract locale from browser Accept-Language header.
     */
    private function getBrowserLocale(Request $request): ?string
    {
        $acceptLanguage = $request->header('Accept-Language');
        if (!$acceptLanguage) {
            return null;
        }

        // Parse Accept-Language header (simplified)
        $languages = explode(',', $acceptLanguage);
        foreach ($languages as $language) {
            $locale = trim(explode(';', $language)[0]);
            $locale = substr($locale, 0, 2); // Get language code only

            if ($this->isValidLocale($locale)) {
                return $locale;
            }
        }

        return null;
    }

    /**
     * Check if locale is valid.
     */
    private function isValidLocale(string $locale): bool
    {
        return in_array($locale, config('locale.allowed_locales', []));
    }

    /**
     * Check if locale detection is enabled for current environment.
     */
    private function isLocaleDetectionEnabled(): bool
    {
        $currentEnv = app()->environment();
        $enabledEnvironments = config('locale.enabled_environments', []);

        return in_array($currentEnv, $enabledEnvironments);
    }
}
