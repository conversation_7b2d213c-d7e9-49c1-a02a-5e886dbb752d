<?php

namespace App\Helpers;

use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\App;

class LocalizationHelper
{
    /**
     * Generate a localized route URL.
     */
    public static function localizedRoute(string $name, array $parameters = [], ?string $locale = null): string
    {
        $locale = $locale ?: App::getLocale();
        $defaultLocale = config('locale.default_locale');

        // If it's the default locale and we hide default locale in URL
        if ($locale === $defaultLocale && config('locale.hide_default_locale_in_url', true)) {
            return route($name, $parameters);
        }

        // For non-default locales, use prefixed route name
        $routeName = $locale !== $defaultLocale ? "{$locale}.{$name}" : $name;
        
        // Check if the localized route exists
        if (Route::has($routeName)) {
            return route($routeName, $parameters);
        }

        // Fallback to default route
        return route($name, $parameters);
    }

    /**
     * Generate URL for switching to a different language while maintaining the current page.
     */
    public static function switchLanguageUrl(string $targetLocale): string
    {
        $request = request();
        $currentRoute = $request->route();
        
        if (!$currentRoute) {
            return self::getHomeUrl($targetLocale);
        }

        $routeName = $currentRoute->getName();
        $parameters = $currentRoute->parameters();

        // Remove locale prefix from route name if present
        $baseRouteName = self::getBaseRouteName($routeName);

        try {
            return self::localizedRoute($baseRouteName, $parameters, $targetLocale);
        } catch (\Exception $e) {
            // If route doesn't exist for target locale, redirect to home
            return self::getHomeUrl($targetLocale);
        }
    }

    /**
     * Get URLs for all available languages for the current page.
     */
    public static function getAlternateLanguageUrls(): array
    {
        $urls = [];
        $allowedLocales = config('locale.allowed_locales', []);

        foreach ($allowedLocales as $locale) {
            $urls[$locale] = self::switchLanguageUrl($locale);
        }

        return $urls;
    }

    /**
     * Get home URL for a specific locale.
     */
    public static function getHomeUrl(string $locale): string
    {
        $defaultLocale = config('locale.default_locale');
        
        if ($locale === $defaultLocale && config('locale.hide_default_locale_in_url', true)) {
            return route('homepage');
        }

        return route("{$locale}.homepage");
    }

    /**
     * Remove locale prefix from route name.
     */
    private static function getBaseRouteName(?string $routeName): string
    {
        if (!$routeName) {
            return 'homepage';
        }

        $allowedLocales = config('locale.allowed_locales', []);
        
        foreach ($allowedLocales as $locale) {
            if (str_starts_with($routeName, "{$locale}.")) {
                return substr($routeName, strlen($locale) + 1);
            }
        }

        return $routeName;
    }

    /**
     * Check if locale detection is enabled for current environment.
     */
    public static function isLocaleDetectionEnabled(): bool
    {
        $currentEnv = app()->environment();
        $enabledEnvironments = config('locale.enabled_environments', []);
        
        return in_array($currentEnv, $enabledEnvironments);
    }

    /**
     * Get current locale with fallback.
     */
    public static function getCurrentLocale(): string
    {
        if (!self::isLocaleDetectionEnabled()) {
            return config('locale.default_locale');
        }

        return App::getLocale();
    }

    /**
     * Check if current locale is the default locale.
     */
    public static function isDefaultLocale(?string $locale = null): bool
    {
        $locale = $locale ?: self::getCurrentLocale();
        return $locale === config('locale.default_locale');
    }

    /**
     * Get locale display name.
     */
    public static function getLocaleDisplayName(string $locale): string
    {
        $names = [
            'el' => 'Ελληνικά',
            'en' => 'English',
        ];

        return $names[$locale] ?? $locale;
    }
}
