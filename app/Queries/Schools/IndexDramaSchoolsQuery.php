<?php

namespace App\Queries\Schools;

use App\Models\School;
use App\Queries\BaseQuery as Query;

class IndexDramaSchoolsQuery extends Query
{
    /**
     * Declare the body of this query
     *
     * @return mixed
     *
     */
    public static function body()
    {
        $request = request();
        // get maximum number of schools per page
        $limit = config('limit.index.festivals.perPage');
        // get current page, default to 1
        $page = $request->input('page', 1);
        // calculate offset
        $offset = ($page * $limit) - $limit;

        // fetch schools from Database
        return School::orderByTranslation('name', 'ASC')
            ->showable()
            ->offset($offset)
            ->limit($limit)
            ->paginate($limit);
    }

}
