<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\School;

class FindMissingSchoolTranslations extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'school-translations:find-missing {--locale=en} {--export}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Find schools with missing translations';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $locale = $this->option('locale');
        $export = $this->option('export');
        
        $this->info("Finding schools missing {$locale} translations...");
        
        $missingTranslations = School::notTranslatedIn($locale)->get();
        $totalSchools = School::count();
        $translatedCount = $totalSchools - $missingTranslations->count();
        
        $this->info("Results:");
        $this->info("- Total Schools: {$totalSchools}");
        $this->info("- Translated ({$locale}): {$translatedCount}");
        $this->info("- Missing translations: {$missingTranslations->count()}");
        $this->info("- Translation coverage: " . round(($translatedCount / $totalSchools) * 100, 2) . "%");
        
        if ($export) {
            $filename = "missing_school_translations_{$locale}_" . date('Y-m-d_H-i-s') . ".csv";
            $filepath = storage_path("app/{$filename}");
            
            $file = fopen($filepath, 'w');
            fputcsv($file, ['ID', 'Name (Greek)', 'Slug', 'Created At']);
            
            foreach ($missingTranslations as $school) {
                fputcsv($file, [
                    $school->id,
                    $school->translate('el')->name ?? 'N/A',
                    $school->slug,
                    $school->created_at
                ]);
            }
            
            fclose($file);
            $this->info("Exported to: {$filepath}");
        }
        
        if ($missingTranslations->count() > 0) {
            $this->info("\nFirst 10 schools missing {$locale} translations:");
            $this->table(
                ['ID', 'Name (Greek)', 'Slug'],
                $missingTranslations->take(10)->map(function ($school) {
                    return [
                        $school->id,
                        $school->translate('el')->name ?? 'N/A',
                        $school->slug
                    ];
                })->toArray()
            );
        }
        
        return 0;
    }
}
