<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\School;
use App\Jobs\UpdateSchoolTranslation;
use Illuminate\Foundation\Bus\DispatchesJobs;

class UpdateSchoolTranslations extends Command
{
    use DispatchesJobs;
    
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'school-translations:update';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Translates school data automatically';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        // fetch some schools
        $schools = School::notTranslatedIn('en')
            ->take(40)
            ->get();

        foreach($schools as $school)
        {
            $this->dispatch(new UpdateSchoolTranslation($school));
        }
        
        $this->info('Dispatched ' . $schools->count() . ' school translation jobs.');
    }
}
