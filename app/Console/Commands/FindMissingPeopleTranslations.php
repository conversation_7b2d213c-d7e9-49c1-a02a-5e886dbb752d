<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Person;

class FindMissingPeopleTranslations extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'people-translations:find-missing {--locale=en} {--export}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Find people with missing translations';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $locale = $this->option('locale');
        $export = $this->option('export');
        
        $this->info("Finding people missing {$locale} translations...");
        
        $missingTranslations = Person::notTranslatedIn($locale)->get();
        $totalPeople = Person::count();
        $translatedCount = $totalPeople - $missingTranslations->count();
        
        $this->info("Results:");
        $this->info("- Total People: {$totalPeople}");
        $this->info("- Translated ({$locale}): {$translatedCount}");
        $this->info("- Missing translations: {$missingTranslations->count()}");
        $this->info("- Translation coverage: " . round(($translatedCount / $totalPeople) * 100, 2) . "%");
        
        if ($export && $missingTranslations->count() > 0) {
            $filename = storage_path("app/missing_people_translations_{$locale}_" . date('Y-m-d_H-i-s') . ".csv");
            
            $file = fopen($filename, 'w');
            fputcsv($file, ['ID', 'First Name (Greek)', 'Last Name (Greek)', 'Bio (Greek)', 'Created At']);
            
            foreach ($missingTranslations as $person) {
                fputcsv($file, [
                    $person->id,
                    $person->translate('el')->first_name ?? 'N/A',
                    $person->translate('el')->last_name ?? 'N/A',
                    substr($person->translate('el')->bio ?? 'N/A', 0, 100) . '...',
                    $person->created_at
                ]);
            }
            
            fclose($file);
            $this->info("Exported missing translations to: {$filename}");
        }
        
        if ($missingTranslations->count() > 0) {
            $this->info("\nFirst 10 people missing {$locale} translations:");
            $this->table(
                ['ID', 'Full Name (Greek)', 'Created At'],
                $missingTranslations->take(10)->map(function ($person) {
                    return [
                        $person->id,
                        $person->translate('el')->first_name . ' ' . $person->translate('el')->last_name ?? 'N/A',
                        $person->created_at
                    ];
                })->toArray()
            );
        }
        
        return 0;
    }
}
