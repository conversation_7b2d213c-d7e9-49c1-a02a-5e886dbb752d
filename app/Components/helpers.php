<?php
/*
|--------------------------------------------------------------------------
| Helper Functions
|--------------------------------------------------------------------------
|
| Here is where you can register helper functions for the application.
| This file is autoloaded through composer.json making the use of
| following functions as easy as it can get
|
*/


use Illuminate\Support\Arr;

if ( ! function_exists('string_to_greeklish'))
{
    function string_to_greeklish($string, $whiteSpaceReplacement = ' ')
    {
        //    $string = str_replace(" - ", "-", $string);
        //    $string = str_replace("- ", "-", $string);
        //    $string = str_replace(" -", "-", $string);
        //    $string = str_replace(". ", ".", $string);

        $str = str_replace(" ", $whiteSpaceReplacement, $string);

        // Replacement rules
        $str = str_replace('α', 'a', $str);
        $str = str_replace('ά', 'a', $str);
        $str = str_replace('Α', 'A', $str);
        $str = str_replace('Ά', 'A', $str);
        $str = str_replace('β', 'b', $str);
        $str = str_replace('Β', 'B', $str);
        $str = str_replace('γ', 'g', $str);
        $str = str_replace('Γ', 'G', $str);
        $str = str_replace('δ', 'd', $str);
        $str = str_replace('Δ', 'D', $str);
        $str = str_replace('ε', 'e', $str);
        $str = str_replace('έ', 'e', $str);
        $str = str_replace('Ε', 'E', $str);
        $str = str_replace('Έ', 'E', $str);
        $str = str_replace('ζ', 'z', $str);
        $str = str_replace('Ζ', 'Z', $str);
        $str = str_replace('η', 'i', $str);
        $str = str_replace('ή', 'i', $str);
        $str = str_replace('Η', 'H', $str);
        $str = str_replace('Ή', 'I', $str);
        $str = str_replace('θ', 'th', $str);
        $str = str_replace('Θ', 'Th', $str);
        $str = str_replace('ι', 'i', $str);
        $str = str_replace('ί', 'i', $str);
        $str = str_replace('ϊ', 'i', $str);
        $str = str_replace('ΐ', 'i', $str);
        $str = str_replace('Ι', 'I', $str);
        $str = str_replace('Ί', 'I', $str);
        $str = str_replace('Ϊ', 'I', $str);
        $str = str_replace('κ', 'k', $str);
        $str = str_replace('Κ', 'K', $str);
        $str = str_replace('λ', 'l', $str);
        $str = str_replace('Λ', 'L', $str);
        $str = str_replace('μ', 'm', $str);
        $str = str_replace('Μ', 'M', $str);
        $str = str_replace('ν', 'n', $str);
        $str = str_replace('Ν', 'N', $str);
        $str = str_replace('ξ', 'ks', $str);
        $str = str_replace('Ξ', 'Ks', $str);
        $str = str_replace('ο', 'o', $str);
        $str = str_replace('ό', 'o', $str);
        $str = str_replace('Ο', 'O', $str);
        $str = str_replace('Ό', 'O', $str);
        $str = str_replace('π', 'p', $str);
        $str = str_replace('Π', 'P', $str);
        $str = str_replace('ρ', 'r', $str);
        $str = str_replace('Ρ', 'R', $str);
        $str = str_replace('σ', 's', $str);
        $str = str_replace('ς', 's', $str);
        $str = str_replace('Σ', 'S', $str);
        $str = str_replace('τ', 't', $str);
        $str = str_replace('Τ', 'T', $str);
        $str = str_replace('υ', 'u', $str);
        $str = str_replace('ύ', 'u', $str);
        $str = str_replace('ϋ', 'u', $str);
        $str = str_replace('ΰ', 'u', $str);
        $str = str_replace('Υ', 'Y', $str);
        $str = str_replace('Ύ', 'Y', $str);
        $str = str_replace('Ϋ', 'Y', $str);
        $str = str_replace('φ', 'f', $str);
        $str = str_replace('Φ', 'F', $str);
        $str = str_replace('χ', 'x', $str);
        $str = str_replace('Χ', 'X', $str);
        $str = str_replace('ψ', 'ps', $str);
        $str = str_replace('Ψ', 'Ps', $str);
        $str = str_replace('ω', 'o', $str);
        $str = str_replace('ώ', 'o', $str);
        $str = str_replace('Ω', 'O', $str);
        $str = str_replace('Ώ', 'O', $str);

        return $str;
    }
}

if ( ! function_exists('greeklish_to_greek'))
{
    function greeklish_to_greek($string, $whiteSpaceReplacement = ' ')
    {
        //    $string = str_replace(" - ", "-", $string);
        //    $string = str_replace("- ", "-", $string);
        //    $string = str_replace(" -", "-", $string);
        //    $string = str_replace(". ", ".", $string);

        $str = str_replace(" ", $whiteSpaceReplacement, $string);

        // Replacement rules
        $str = str_replace('ch', 'χ', $str);
        $str = str_replace('Ch', 'Χ', $str);
        $str = str_replace('ps', 'ψ', $str);
        $str = str_replace('Ps', 'Ψ', $str);
        $str = str_replace('ks', 'ξ', $str);
        $str = str_replace('Ks', 'Ξ', $str);
        $str = str_replace('ps', 'ψ', $str);
        $str = str_replace('Ps', 'Ψ', $str);
        $str = str_replace('a', 'α', $str);
        $str = str_replace('A', 'Α', $str);
        $str = str_replace('b', 'β', $str);
        $str = str_replace('B', 'Β', $str);
        $str = str_replace('g', 'γ', $str);
        $str = str_replace('G', 'Γ', $str);
        $str = str_replace('d', 'δ', $str);
        $str = str_replace('D', 'Δ', $str);
        $str = str_replace('e', 'ε', $str);
        $str = str_replace('E', 'Ε', $str);
        $str = str_replace('z', 'ζ', $str);
        $str = str_replace('Z', 'Ζ', $str);
        $str = str_replace('h', 'η', $str);
        $str = str_replace('H', 'Η', $str);
        $str = str_replace('th', 'θ', $str);
        $str = str_replace('Th', 'Θ', $str);
        $str = str_replace('i', 'ι', $str);
        $str = str_replace('I', 'Ι', $str);
        $str = str_replace('k', 'κ', $str);
        $str = str_replace('K', 'Κ', $str);
        $str = str_replace('l', 'λ', $str);
        $str = str_replace('L', 'Λ', $str);
        $str = str_replace('m', 'μ', $str);
        $str = str_replace('M', 'Μ', $str);
        $str = str_replace('n', 'ν', $str);
        $str = str_replace('N', 'Ν', $str);
        $str = str_replace('o', 'ο', $str);
        $str = str_replace('O', 'Ο', $str);
        $str = str_replace('p', 'π', $str);
        $str = str_replace('P', 'Π', $str);
        $str = str_replace('r', 'ρ', $str);
        $str = str_replace('R', 'Ρ', $str);
        $str = str_replace('s', 'σ', $str);
        $str = str_replace('S', 'Σ', $str);
        $str = str_replace('t', 'τ', $str);
        $str = str_replace('T', 'Τ', $str);
        $str = str_replace('u', 'υ', $str);
        $str = str_replace('Y', 'Υ', $str);
        $str = str_replace('f', 'φ', $str);
        $str = str_replace('F', 'Φ', $str);
        $str = str_replace('x', 'χ', $str);
        $str = str_replace('X', 'Χ', $str);
        $str = str_replace('w', 'ω', $str);
        $str = str_replace('W', 'Ω', $str);

        return $str;
    }
}

if ( ! function_exists('replace_greek_accented_characters'))
{
    function replace_greek_accented_characters($string)
    {
        $string = str_replace('ά', 'α', $string);
        $string = str_replace('Ά', 'Α', $string);
        $string = str_replace('έ', 'ε', $string);
        $string = str_replace('Έ', 'Ε', $string);
        $string = str_replace('ή', 'η', $string);
        $string = str_replace('Ή', 'Η', $string);
        $string = str_replace('ί', 'ι', $string);
        $string = str_replace('ϊ', 'ι', $string);
        $string = str_replace('ΐ', 'ι', $string);
        $string = str_replace('Ί', 'Ι', $string);
        $string = str_replace('Ϊ', 'Ι', $string);
        $string = str_replace('ό', 'ο', $string);
        $string = str_replace('Ό', 'Ο', $string);
        $string = str_replace('ύ', 'υ', $string);
        $string = str_replace('ϋ', 'υ', $string);
        $string = str_replace('ΰ', 'υ', $string);
        $string = str_replace('Ύ', 'Υ', $string);
        $string = str_replace('Ϋ', 'Υ', $string);
        $string = str_replace('ώ', 'ω', $string);
        $string = str_replace('Ώ', 'ω', $string);

        return $string;
    }
}

if ( ! function_exists('link_to_route'))
{
    /**
     * Generate a HTML link to a named route.
     *
     * @param  string $name
     * @param  string $title
     * @param  array $parameters
     * @param  array $attributes
     * @return string
     */
    function link_to_route($name, $title = null, $parameters = [], $attributes = [])
    {
        $url = route($name, $parameters);

        if (is_null($title) || $title === false)
        {
            $title = $url;
        }

        $title = htmlentities($title, ENT_QUOTES, 'UTF-8', false);

        $html = [];

        foreach ((array)$attributes as $key => $value)
        {
            if (is_numeric($key))
                $key = $value;

            if ( ! is_null($value))
            {
                $element = $key . '="' . e($value) . '"';
            }

            if ( ! is_null($element))
                $html[] = $element;
        }

        $attributes = count($html) > 0 ? ' ' . implode(' ', $html) : '';


        return '<a href="' . $url . '"' . $attributes . '>' . $title . '</a>';

    }
}

if ( ! function_exists('remove_greek_stopwords'))
{
    function remove_greek_stopwords($string)
    {
        $stopwords = [
            'ΑΙ', 'ΑΚΟΜΑ', 'ΑΚΟΜΗ', 'ΑΛΛΑ', 'ΑΛΛΕΣ', 'ΑΛΛΗ', 'ΑΛΛΗΝ', 'ΑΛΛΗΣ', 'ΑΛΛΙΩΣ', 'ΑΛΛΟ', 'ΑΛΛΟΙ', 'ΑΛΛΟΙΩΣ', 'ΑΛΛΟΝ', 'ΑΛΛΟΣ', 'ΑΛΛΟΤΕ', 'ΑΛΛΟΥ', 'ΑΛΛΟΥΣ', 'ΑΛΛΩΝ', 'ΑΜΑ', 'ΑΜΕΣΑ', 'ΑΝ', 'ΑΝΑ', 'ΑΝΑΜΕΣΑ', 'ΑΝΕΥ', 'ΑΝΤΙ', 'ΑΝΤΙΣ', 'ΑΝΩ', 'ΑΝΩΤΕΡΩ', 'ΑΞΑΦΝΑ', 'ΑΠ', 'ΑΠΟ', 'ΑΠΟΨΕ', 'ΑΡΑ', 'ΑΡΑΓΕ', 'ΑΡΓΑ', 'ΑΡΚΕΤΑ', 'ΑΡΧΙΚΑ', 'ΑΣ', 'ΑΥΡΙΟ', 'ΑΥΤΑ', 'ΑΥΤΕΣ', 'ΑΥΤΗ', 'ΑΥΤΗΝ', 'ΑΥΤΗΣ', 'ΑΥΤΟ', 'ΑΥΤΟΙ', 'ΑΥΤΟΝ', 'ΑΥΤΟΣ', 'ΑΥΤΟΥ', 'ΑΥΤΟΥΣ', 'ΑΥΤΩΝ', 'ΑΦΟΤΟΥ', 'ΑΦΟΥ', 'ΒΕΒΑΙΑ', 'ΓΙ', 'ΓΙΑ', '∆Α', '∆Ε', '∆ΕΝ', '∆ΕΞΙΑ', '∆Ι', '∆ΙΑ', '∆ΙΚΑ', '∆ΙΚΟ', '∆ΙΚΟΙ', '∆ΙΚΟΣ', '∆ΙΚΟΥ', '∆ΙΚΟΥΣ', '∆ΙΟΛΟΥ', '∆ΙΠΛΑ', '∆ΙΧΩΣ', 'ΕΑΝ', 'ΕΓΩ', 'Ε∆Ω', 'ΕΙΘΕ', 'ΕΙΜΑΙ', 'ΕΙΜΑΣΤΕ', 'ΕΙΝΑΙ', 'ΕΙΣ', 'ΕΙΣΑΙ', 'ΕΙΣΑΣΤΕ', 'ΕΙΣΤΕ', 'ΕΙΤΕ', 'ΕΙΧΑ', 'ΕΙΧΑΜΕ', 'ΕΙΧΑΝ', 'ΕΙΧΑΤΕ', 'ΕΙΧΕ', 'ΕΙΧΕΣ', 'ΕΚΕΙ', 'ΕΚΕΙΝΑ', 'ΕΚΕΙΝΕΣ', 'ΕΚΕΙΝΗ', 'ΕΚΕΙΝΗΝ', 'ΕΚΕΙΝΗΣ', 'ΕΚΕΙΝΟ', 'ΕΚΕΙΝΟΙ', 'ΕΚΕΙΝΟΝ', 'ΕΚΕΙΝΟΣ', 'ΕΚΕΙΝΟΥ', 'ΕΚΕΙΝΟΥΣ', 'ΕΚΕΙΝΩΝ', 'ΕΚΤΟΣ', 'ΕΜΑΣ', 'ΕΜΕΙΣ', 'ΕΜΕΝΑ', 'ΕΜΠΡΟΣ', 'ΕΝ', 'ΕΝΑ', 'ΕΝΑΝ', 'ΕΝΑΣ', 'ΕΝΟΣ', 'ΕΝΤΟΣ', 'ΕΝΩ', 'ΕΞ', 'ΕΞΗΣ', 'ΕΞΩ', 'ΕΠΙ', 'ΕΠΙΣΗΣ', 'ΕΣΑΣ', 'ΕΣΕΙΣ', 'ΕΣΕΝΑ', 'ΕΣΤΩ', 'ΕΣΥ', 'ΕΤΕΡΑ', 'ΕΤΕΡΑΙ', 'ΕΤΕΡΑΣ', 'ΕΤΕΡΕΣ', 'ΕΤΕΡΗ', 'ΕΤΕΡΗΣ', 'ΕΤΕΡΟ', 'ΕΤΕΡΟΙ', 'ΕΤΕΡΟΝ', 'ΕΤΕΡΟΣ', 'ΕΤΕΡΟΥ', 'ΕΤΕΡΟΥΣ', 'ΕΤΕΡΩΝ', 'ΕΤΟΥΤΑ', 'ΕΤΟΥΤΕΣ', 'ΕΤΟΥΤΗ', 'ΕΤΟΥΤΗΝ', 'ΕΤΟΥΤΗΣ', 'ΕΤΟΥΤΟ', 'ΕΤΟΥΤΟΙ', 'ΕΤΟΥΤΟΝ', 'ΕΤΟΥΤΟΣ', 'ΕΤΟΥΤΟΥ', 'ΕΤΟΥΤΟΥΣ', 'ΕΤΟΥΤΩΝ', 'ΕΤΣΙ', 'ΕΧΕΙ', 'ΕΧΕΙΣ', 'ΕΧΕΤΕ', 'ΕΧΘΕΣ', 'ΕΧΟΜΕ', 'ΕΧΟΥΜΕ', 'ΕΧΟΥΝ', 'ΕΧΤΕΣ', 'ΕΧΩ', 'ΕΩΣ', 'Η', 'Η∆Η', 'ΗΜΑΣΤΑΝ', 'ΗΜΑΣΤΕ', 'ΗΜΟΥΝ', 'ΗΣΑΣΤΑΝ', 'ΗΣΑΣΤΕ', 'ΗΣΟΥΝ', 'ΗΤΑΝ', 'ΗΤΑΝΕ', 'ΗΤΟΙ', 'ΗΤΤΟΝ', 'ΘΑ', 'Ι', 'Ι∆ΙΑ', 'Ι∆ΙΑΝ', 'Ι∆ΙΑΣ', 'Ι∆ΙΕΣ', 'Ι∆ΙΟ', 'Ι∆ΙΟΙ', 'Ι∆ΙΟΝ', 'Ι∆ΙΟΣ', 'Ι∆ΙΟΥ', 'Ι∆ΙΟΥΣ', 'Ι∆ΙΩΝ', 'Ι∆ΙΩΣ', 'ΙΙ', 'ΙΙΙ', 'ΙΣΑΜΕ', 'ΙΣΙΑ', 'ΙΣΩΣ', 'ΚΑΘΕ', 'ΚΑΘΕΜΙΑ', 'ΚΑΘΕΜΙΑΣ', 'ΚΑΘΕΝΑ', 'ΚΑΘΕΝΑΣ', 'ΚΑΘΕΝΟΣ', 'ΚΑΘΕΤΙ', 'ΚΑΘΟΛΟΥ', 'ΚΑΘΩΣ', 'ΚΑΙ', 'ΚΑΚΑ', 'ΚΑΚΩΣ', 'ΚΑΛΑ', 'ΚΑΛΩΣ', 'ΚΑΜΙΑ', 'ΚΑΜΙΑΝ', 'ΚΑΜΙΑΣ', 'ΚΑΝΕΙΣ', 'ΚΑΝΕΝ', 'ΚΑΝΕΝΑ', 'ΚΑΝΕΝΑΝ', 'ΚΑΝΕΝΑΣ', 'ΚΑΝΕΝΟΣ', 'ΚΑΠΟΙΑ', 'ΚΑΠΟΙΑΝ', 'ΚΑΠΟΙΑΣ', 'ΚΑΠΟΙΕΣ', 'ΚΑΠΟΙΟ', 'ΚΑΠΟΙΟΙ', 'ΚΑΠΟΙΟΝ', 'ΚΑΠΟΙΟΣ', 'ΚΑΠΟΙΟΥ', 'ΚΑΠΟΙΟΥΣ', 'ΚΑΠΟΙΩΝ', 'ΚΑΠΟΤΕ', 'ΚΑΠΟΥ', 'ΚΑΠΩΣ', 'ΚΑΤ', 'ΚΑΤΑ', 'ΚΑΤΙ', 'ΚΑΤΩ', 'ΚΙΟΛΑΣ', 'ΚΛΠ', 'ΚΤΛ', 'ΛΟΓΩ', 'ΜΑ', 'ΜΑΖΙ', 'ΜΑΛΛΟΝ', 'ΜΑΣ', 'ΜΕ', 'ΜΕΛΕΙ', 'ΜΕΜΙΑΣ', 'ΜΕΝ', 'ΜΕΡΙΚΑ', 'ΜΕΡΙΚΕΣ', 'ΜΕΡΙΚΟΙ', 'ΜΕΡΙΚΟΥΣ', 'ΜΕΡΙΚΩΝ', 'ΜΕΣΑ', 'ΜΕΤ', 'ΜΕΤΑ', 'ΜΕΤΑΞΥ', 'ΜΕΧΡΙ', 'ΜΗ', 'ΜΗ∆Ε', 'ΜΗΝ', 'ΜΗΠΩΣ', 'ΜΗΤΕ', 'ΜΙΑ', 'ΜΙΑΝ', 'ΜΙΑΣ', 'ΜΟΛΙΣ', 'ΜΟΝΕΣ', 'ΜΟΝΗ', 'ΜΟΝΗΝ', 'ΜΟΝΗΣ', 'ΜΟΝΟ', 'ΜΟΝΟΙ', 'ΜΟΝΟΣ', 'ΜΟΝΟΥ', 'ΜΟΝΟΥΣ', 'ΜΟΝΩΝ', 'ΜΟΥ', 'ΜΠΟΡΕΙ', 'ΜΠΟΡΟΥΝ', 'ΜΠΡΑΒΟ', 'ΜΠΡΟΣ', 'ΝΑ', 'ΝΑΙ', 'ΝΩΡΙΣ', 'ΞΑΝΑ', 'ΞΑΦΝΙΚΑ', 'Ο', 'ΟΙ', 'ΟΛΑ', 'ΟΛΕΣ', 'ΟΛΗ', 'ΟΛΗΝ', 'ΟΛΗΣ', 'ΟΛΟ', 'ΟΛΟΓΥΡΑ', 'ΟΛΟΙ', 'ΟΛΟΝ', 'ΟΛΟΝΕΝ', 'ΟΛΟΣ', 'ΟΛΟΥ', 'ΟΛΟΥΣ', 'ΟΛΩΝ', 'ΟΛΩΣ', 'ΟΜΩΣ', 'ΟΠΟΙΑ', 'ΟΠΟΙΑ∆ΗΠΟΤΕ', 'ΟΠΟΙΑΝ', 'ΟΠΟΙΑΝ∆ΗΠΟΤΕ', 'ΟΠΟΙΑΣ', 'ΟΠΟΙΑΣ∆ΗΠΟΤΕ', 'ΟΠΟΙ∆ΗΠΟΤΕ', 'ΟΠΟΙΕΣ', 'ΟΠΟΙΕΣ∆ΗΠΟΤΕ', 'ΟΠΟΙΟ', 'ΟΠΟΙΟ∆ΗΠΟΤΕ', 'ΟΠΟΙΟΙ', 'ΟΠΟΙΟΝ', 'ΟΠΟΙΟΝ∆ΗΠΟΤΕ', 'ΟΠΟΙΟΣ', 'ΟΠΟΙΟΣ∆ΗΠΟΤΕ', 'ΟΠΟΙΟΥ', 'ΟΠΟΙΟΥ∆ΗΠΟΤΕ', 'ΟΠΟΙΟΥΣ', 'ΟΠΟΙΟΥΣ∆ΗΠΟΤΕ', 'ΟΠΟΙΩΝ', 'ΟΠΟΙΩΝ∆ΗΠΟΤΕ', 'ΟΠΟΤΕ', 'ΟΠΟΤΕ∆ΗΠΟΤΕ', 'ΟΠΟΥ', 'ΟΠΟΥ∆ΗΠΟΤΕ', 'ΟΠΩΣ', 'ΟΡΙΣΜΕΝΑ', 'ΟΡΙΣΜΕΝΕΣ', 'ΟΡΙΣΜΕΝΩΝ', 'ΟΡΙΣΜΕΝΩΣ', 'ΟΣΑ', 'ΟΣΑ∆ΗΠΟΤΕ', 'ΟΣΕΣ', 'ΟΣΕΣ∆ΗΠΟΤΕ', 'ΟΣΗ', 'ΟΣΗ∆ΗΠΟΤΕ', 'ΟΣΗΝ', 'ΟΣΗΝ∆ΗΠΟΤΕ', 'ΟΣΗΣ', 'ΟΣΗΣ∆ΗΠΟΤΕ', 'ΟΣΟ', 'ΟΣΟ∆ΗΠΟΤΕ', 'ΟΣΟΙ', 'ΟΣΟΙ∆ΗΠΟΤΕ', 'ΟΣΟΝ', 'ΟΣΟΝ∆ΗΠΟΤΕ', 'ΟΣΟΣ', 'ΟΣΟΣ∆ΗΠΟΤΕ', 'ΟΣΟΥ', 'ΟΣΟΥ∆ΗΠΟΤΕ', 'ΟΣΟΥΣ', 'ΟΣΟΥΣ∆ΗΠΟΤΕ', 'ΟΣΩΝ', 'ΟΣΩΝ∆ΗΠΟΤΕ', 'ΟΤΑΝ', 'ΟΤΙ', 'ΟΤΙ∆ΗΠΟΤΕ', 'ΟΤΟΥ', 'ΟΥ', 'ΟΥ∆Ε', 'ΟΥΤΕ', 'ΟΧΙ', 'ΠΑΛΙ', 'ΠΑΝΤΟΤΕ', 'ΠΑΝΤΟΥ', 'ΠΑΝΤΩΣ', 'ΠΑΡΑ', 'ΠΕΡΑ', 'ΠΕΡΙ', 'ΠΕΡΣΙ', 'ΠΕΡΥΣΙ', 'ΠΙΑ', 'ΠΙΘΑΝΟΝ', 'ΠΙΟ', 'ΠΙΣΩ', 'ΠΛΑΙ', 'ΠΛΕΟΝ', 'ΠΛΗΝ', 'ΠΟΙΑ', 'ΠΟΙΑΝ', 'ΠΟΙΑΣ', 'ΠΟΙΕΣ', 'ΠΟΙΟ', 'ΠΟΙΟΙ', 'ΠΟΙΟΝ', 'ΠΟΙΟΣ', 'ΠΟΙΟΥ', 'ΠΟΙΟΥΣ', 'ΠΟΙΩΝ', 'ΠΟΛΥ', 'ΠΟΣΕΣ', 'ΠΟΣΗ', 'ΠΟΣΗΝ', 'ΠΟΣΗΣ', 'ΠΟΣΟΙ', 'ΠΟΣΟΣ', 'ΠΟΣΟΥΣ', 'ΠΟΤΕ', 'ΠΟΥ', 'ΠΟΥΘΕ', 'ΠΟΥΘΕΝΑ', 'ΠΡΕΠΕΙ', 'ΠΡΙΝ', 'ΠΡΟ', 'ΠΡΟΣ', 'ΠΩΣ', 'ΣΑΝ', 'ΣΑΣ', 'ΣΕ', 'ΣΕΙΣ', 'ΣΗΜΕΡΑ', 'ΣΙΓΑ', 'ΣΟΥ', 'ΣΤΑ', 'ΣΤΗ', 'ΣΤΗΝ', 'ΣΤΗΣ', 'ΣΤΙΣ', 'ΣΤΟ', 'ΣΤΟΝ', 'ΣΤΟΥ', 'ΣΤΟΥΣ', 'ΣΤΩΝ', 'ΣΥΝ', 'ΤΑ', 'ΤΑ∆Ε', 'ΤΑΥΤΑ', 'ΤΑΥΤΕΣ', 'ΤΑΥΤΗ', 'ΤΑΥΤΗΝ', 'ΤΑΥΤΗΣ', 'ΤΑΥΤΟΣ', 'ΤΑΥΤΟΥ', 'ΤΑΥΤΩΝ', 'ΤΑΧΑ', 'ΤΑΧΑΤΕ', 'ΤΕΛΙΚΑ', 'ΤΕΛΙΚΩΣ', 'ΤΕΣ', 'ΤΕΤΟΙΑ', 'ΤΕΤΟΙΑΝ', 'ΤΕΤΟΙΑΣ', 'ΤΕΤΟΙΕΣ', 'ΤΕΤΟΙΟ', 'ΤΕΤΟΙΟΙ', 'ΤΕΤΟΙΟΝ', 'ΤΕΤΟΙΟΣ', 'ΤΕΤΟΙΟΥ', 'ΤΕΤΟΙΟΥΣ', 'ΤΕΤΟΙΩΝ', 'ΤΗ', 'ΤΗΝ', 'ΤΗΣ', 'ΤΙ', 'ΤΙΠΟΤΑ', 'ΤΙΠΟΤΕ', 'ΤΙΣ', 'ΤΟ', 'ΤΟΙ', 'ΤΟΝ', 'ΤΟΣ', 'ΤΟΣΑ', 'ΤΟΣΕΣ', 'ΤΟΣΗ', 'ΤΟΣΗΝ', 'ΤΟΣΗΣ', 'ΤΟΣΟ', 'ΤΟΣΟΙ', 'ΤΟΣΟΝ', 'ΤΟΣΟΣ', 'ΤΟΣΟΥ', 'ΤΟΣΟΥΣ', 'ΤΟΣΩΝ', 'ΤΟΤΕ', 'ΤΟΥ', 'ΤΟΥΣ', 'ΤΟΥΤΑ', 'ΤΟΥΤΕΣ', 'ΤΟΥΤΗ', 'ΤΟΥΤΗΝ', 'ΤΟΥΤΗΣ', 'ΤΟΥΤΟ', 'ΤΟΥΤΟΙ', 'ΤΟΥΤΟΙΣ', 'ΤΟΥΤΟΝ', 'ΤΟΥΤΟΣ', 'ΤΟΥΤΟΥ', 'ΤΟΥΤΟΥΣ', 'ΤΟΥΤΩΝ', 'ΤΥΧΟΝ', 'ΤΩΝ', 'ΤΩΡΑ', 'ΥΠ', 'ΥΠΕΡ', 'ΥΠΟ', 'Ω', 'ΩΡΑΙΑ', 'ΩΣ', 'ΩΣΑΝ', 'ΩΣΟΤΟΥ', 'ΩΣΠΟΥ', 'ΩΣΤΕ',
        ];
        $input = explode(' ', $string);

        foreach ($input as $key => $word)
        {
            if (in_array(mb_strtolower($word), $stopwords) || in_array(mb_strtoupper($word), $stopwords))
            {
                Arr::forget($input, $key);
            }
        }

        return $input;
    }

}

if ( ! function_exists('unstageAsset')) {
    /**
     * Generate an asset path for the application.
     *
     * @param  string  $path
     * @param  bool    $secure
     * @return string
     */
    function unstageAsset($path, $secure = null)
    {
        $asset = asset($path, $secure);

        if (env('APP_SERVE_PRODUCTION_ASSETS', false)) {
            $asset = str_replace(
                "http" . (request()->isSecure() ? "s" : "") . "://" . request()->getHost(),
                'https://www.unstage.gr',
                $asset);
        }

        return $asset;
    }
}

if ( ! function_exists('setUnstageLocale')) {
    /**
     * Generate an asset path for the application.
     *
     * @param  string  $path
     * @param  bool    $secure
     * @return string
     */
    function setUnstageLocale()
    {
        // Enable for local and staging environments
        if(!env('APP_DEBUG') && !in_array(env('APP_ENV'), ['local', 'staging']))
        {
            return true;
        }

        app()->setLocale(
            app('request')->segment(1) && in_array(app('request')->segment(1), config('locale.allowed_locales')) ?
                app('request')->segment(1) :
                config('locale.default_locale')
        );

        return true;
    }
}

if ( ! function_exists('localizedRoute')) {
    /**
     * Generate a localized route URL.
     *
     * @param  string  $name
     * @param  array   $parameters
     * @param  string|null  $locale
     * @return string
     */
    function localizedRoute($name, $parameters = [], $locale = null)
    {
        $locale = $locale ?: app()->getLocale();

        // If it's the default locale, don't add prefix
        if ($locale === config('locale.default_locale')) {
            return route($name, $parameters);
        }

        // For non-default locales, we need to manually construct the URL
        // since Laravel routes are already prefixed in RouteServiceProvider
        return route($name, $parameters);
    }
}

if ( ! function_exists('switchLanguageUrl')) {
    /**
     * Generate URL for switching to a different language while maintaining the current page.
     *
     * @param  string  $targetLocale
     * @return string
     */
    function switchLanguageUrl($targetLocale)
    {
        $request = request();
        $currentLocale = app()->getLocale();
        $defaultLocale = config('locale.default_locale');

        // Get current URL
        $currentUrl = $request->url();
        $currentPath = $request->getPathInfo();

        // If switching to default locale
        if ($targetLocale === $defaultLocale) {
            // If current locale is default, return current URL
            if ($currentLocale === $defaultLocale) {
                return $currentUrl;
            }
            // Remove current locale prefix
            $newPath = preg_replace('/^\/' . $currentLocale . '(\/|$)/', '/', $currentPath);
            return $request->getSchemeAndHttpHost() . ($newPath === '/' ? '' : $newPath);
        }

        // For non-default target locales
        if ($currentLocale === $defaultLocale) {
            // Add target locale prefix
            $newPath = '/' . $targetLocale . $currentPath;
        } else {
            // Replace current locale with target locale
            $newPath = preg_replace('/^\/' . $currentLocale . '(\/|$)/', '/' . $targetLocale . '$1', $currentPath);
        }

        return $request->getSchemeAndHttpHost() . $newPath;
    }
}

if ( ! function_exists('getAlternateLanguageUrls')) {
    /**
     * Get URLs for all available languages for the current page.
     *
     * @return array
     */
    function getAlternateLanguageUrls()
    {
        $urls = [];
        $allowedLocales = config('locale.allowed_locales');

        foreach ($allowedLocales as $locale) {
            $urls[$locale] = switchLanguageUrl($locale);
        }

        return $urls;
    }
}

if ( ! function_exists('getCurrentPageInLanguage')) {
    /**
     * Check if current page exists in target language.
     * For now, we'll assume all pages exist in both languages.
     * This can be enhanced later to check for translated content.
     *
     * @param  string  $targetLocale
     * @return bool
     */
    function getCurrentPageInLanguage($targetLocale)
    {
        // For now, return true for all pages
        // This can be enhanced to check if specific content is translated
        return true;
    }
}
