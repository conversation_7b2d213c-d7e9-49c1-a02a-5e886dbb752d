<?php

namespace App\Models;

use App\Models\Overrides\Model;
use App\Models\Traits\Publishable;
use App\Models\Traits\Relationships\TvShowRelationships;
use App\Models\Traits\Revisionable;
use Carbon\Carbon;
use Croppa;
use C<PERSON>brock\EloquentSluggable\Sluggable;
use Illuminate\Database\Eloquent\Builder;
use Astrotomic\Translatable\Contracts\Translatable as TranslatableContract;
use Astrotomic\Translatable\Translatable;

class TvShow extends Model implements TranslatableContract
{
    protected $table = 'tv_shows';

    use Sluggable, Revisionable, TvShowRelationships, Publishable, Translatable;

    public $translationForeignKey = 'tv_show_id';
    public $translationModel = '\App\Models\TvShowTranslation';
    public $translatedAttributes = ['title', 'synopsis'];

    protected $revisionCreationsEnabled = true;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'duration',
        'year',
        'imdb',
        'tv_channel_id',
        'published',
        'finalised',
        'moderated',
        'notes',
        'user_id',
        'user_notes',
    ];

    /**
     * Return the sluggable configuration array for this model.
     *
     * @return array
     */
    public function sluggable(): array
    {
        return [
            'slug' => [
                'source' => 'title_year_slug',
            ],
        ];
    }


    /**
     * Mutator for tv channel attribute
     *
     * @param $value
     */
    public function setTvChannelIdAttribute($value)
    {
        if ( ! $value) {
            $this->attributes['tv_channel_id'] = null;
        }
        else {
            $this->attributes['tv_channel_id'] = $value;
        }
    }

    /**
     * Mut
     * ator for year attribute
     *
     * @param $value
     */
    public function setYearAttribute($value)
    {
        if ( ! $value) {
            $this->attributes['year'] = null;
        }
        else {
            $this->attributes['year'] = $value;
        }
    }


// =======================================================================//
//                            Various Getter Mefods
// =======================================================================//
    /**
     * Get the title/year combo for slug generation
     *
     * @return string
     */
    public function getTitleYearSlugAttribute()
    {
        $title = $this->title ?: '';
        $year = $this->year ?: '';

        return trim($title . ' ' . $year);
    }

    /**
     * Get the title/year combo in one go
     *
     * @return string
     */
    public function getFulltitleAttribute()
    {
        if(!empty($this->tvChannel()->first()))
        {
            return trim($this->title) . ' (' . $this->tvChannel()->first()->name . ', ' . trim($this->year) . ')';
        }
        else
        {
            return trim($this->title) . ' (' . trim($this->year) . ')';
        }
    }


    /**
     * Get the rating score of a tv_show
     * @return int|string
     */
    public function getRatingScore()
    {
        return str_replace(',0', '', number_format($this->ratings()->avg('rating'), 1, ',', ''));
    }

    /**
     * Get the number of times this tv show was rated
     * @return mixed
     */
    public function getRatingCount()
    {
        return $this->ratings()->count();
    }

    /**
     * Get a list of film genre ids associated with this tv show
     *
     * @return array
     */
    public function getFilmGenresIdsAttribute()
    {
        if ( ! $this->relationLoaded('filmGenres')) {
            return null;
        }

        return $this->getRelation('filmGenres')->pluck('id')->all();
    }

    /**
     * Get a list of film genre names associated with current tv show
     *
     * @return array
     */
    public function getFilmGenreNamesAttribute()
    {
        if ( ! isset($this->getRelations()['filmGenres'])) {
            return null;
        }

        return implode(', ', $this->getRelation('filmGenres')->pluck('name')->all());
    }


// =======================================================================//
//                               Scopes
// =======================================================================//
    public function scopeArchive(Builder $query)
    {
        return $query->orderBy('year', 'desc');
    }

    /**
     * All tv shows that are unmoderated
     *
     * @param Builder $query
     */
    public function scopeUnmoderated(Builder $query)
    {
        $query->where('moderated', false);
    }

    public function scopeTrending(Builder $query, $limit = null)
    {
        if ( ! $limit) {
            $limit = config('limit.plays.trending');
        }

        // Limit equals to the TOTAL number of movies we want to show.
        // So if we want to show 2 trending movies for 2 roles limit should be 4 (2*2).
        // Keep in mind that if a role has only one trending then second role can have up to 3 trending movies.
//        return $query->orderBy('internal_rating', 'DESC')
//            ->limit($limit);
        return $query->limit($limit);
    }

    /**
     * Scope showable
     * All tvShows that are published
     *
     * @param Builder $query
     * @param $limit
     */
    public function scopeShowable(Builder $query)
    {
        $query->where('published', true);
    }

    // Note: scopeNotTranslatedIn is provided by Astrotomic\Translatable\Translatable trait

//    public function scopeRequiredHavingImages(Builder $query)
//    {
//        $query->join('images', 'images.theatric_play_id', '=', 'theatric_plays.id')->where('images.main', 1);
//    }
}
