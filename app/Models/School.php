<?php

namespace App\Models;

use App\Models\Traits\Relationships\SchoolRelationships;
use App\Models\Traits\Scopes\SchoolScopes;
use Cviebrock\EloquentSluggable\Sluggable;
use App\Models\Overrides\Model;
use Illuminate\Database\Eloquent\Builder;
use Astrotomic\Translatable\Contracts\Translatable as TranslatableContract;
use Astrotomic\Translatable\Translatable;

class School extends Model implements TranslatableContract
{
    use Sluggable,
        SchoolRelationships,
        SchoolScopes,
        Translatable;

    public $translationForeignKey = 'school_id';
    public $translationModel = '\App\Models\SchoolTranslation';
    public $translatedAttributes = ['name', 'description'];

    protected $fillable = [
        'address',
        'city',
        'area',
        'longitude',
        'latitude',
        'email',
        'second_email',
        'phone',
        'mobile_phone',
        'facebook',
        'twitter',
        'website',
        'showable',
    ];


    /**
     * Return the sluggable configuration array for this model.
     *
     * @return array
     */
    public function sluggable(): array
    {
        return [
            'slug' => [
                'source' => 'slugSource',
            ],
        ];
    }

    /**
     * Get the source for slug generation
     * Uses the translated name from the current locale
     */
    public function getSlugSourceAttribute()
    {
        return $this->name;
    }


    // =======================================================================//
    //                         Accessors & Mutators
    // =======================================================================//

    public function getFullAddressAttribute()
    {
        if ($this->address && $this->city)
        {
            return $this->address . ', ' . $this->city;
        }
        elseif ($this->address)
        {
            return $this->address;
        }

        return null;
    }

    // Note: scopeNotTranslatedIn is provided by Astrotomic\Translatable\Translatable trait

}
