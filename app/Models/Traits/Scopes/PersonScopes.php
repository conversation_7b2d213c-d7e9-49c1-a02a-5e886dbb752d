<?php

namespace App\Models\Traits\Scopes;

use Illuminate\Database\Eloquent\Builder;

trait PersonScopes
{
    public function scopeRequiredHavingImages($query)
    {
        $query->join('images', 'images.person_id', '=', 'people.id')->where('images.main', 1);
    }


    /**
     * Get the profilable people
     *
     * @param $query
     * @return mixed
     */
    public function scopeProfilable($query)
    {
        return $query->where('profilable', true);
    }

    // Note: scopeNotTranslatedIn is provided by Astrotomic\Translatable\Translatable trait
    // No need to define it here as it would cause a collision


}
