html {
    font-size: 14px;
}

body {
    color: #3b4241;
    background-color: #fff;
    font-family: "PF BeauSans W15 Regular", "Roboto", Cambria, Georgia, serif;
    -webkit-font-smoothing: antialiased;
    font-size: 15px;
    font-weight: normal;
    font-style: normal;
    line-height: 1.5;
    position: relative;
}

.text-right {
    text-align: right
}

@media screen and (min-width: 992px) {
    .modal-open {
        /*overflow: auto;*/
    }

    .modal {
        bottom: -2px;
    }
}

@media screen and (max-width: 991px) {

    body.modal-open {
    }
}

@media screen and (max-width: 767px) {

    html, body {
        font-size: 13px;
    }

}

a {
    color: #222;
    outline: none !important;
}

a:hover, a:focus {
    text-decoration: none;
    color: #222;
}

.hover-underline:hover {
    text-decoration: underline;
}

.hover-underline:hover .circle-hover {
    opacity: 0.9;
}

h1, h2, h3, h4, h5, h6 {
    margin: 0;
}

b, strong {
    font-family: "PF BeauSans W15 SemiBold", "Roboto", Cambria, Georgia, serif;
}

.table .table {
    background-color: transparent;
}

.datepickers-container {
    z-index: 5002;
}

.modal-header--noBorder {
    border: 0;
}

.dropdown-menu .divider {
    margin: 0;
}

/* ==========================================================================
    Sticky flexbox footer
   ========================================================================== */

html {
    box-sizing: border-box;
}

*, *:before, *:after {
    box-sizing: inherit;
}

.wrapper {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    flex: 1;
}

.flex-header {
}

.flex-footer {
}

.flex-body {
    flex: 1 0 auto;
    flex: 1;
}

/* ==========================================================================
    General
   ========================================================================== */
label {
    font-size: 13px;
    font-weight: 400;
}

.menu-items {
    padding: 2px 15px;
}

.menu-items-link {
    padding: 3px 15px 2px;
}

.menu-items-link--text {
    padding: 11px 15px 2px;
}

.dropdown-menu > li > a {
    padding: 6px 30px;
}

@media (max-width: 991px) {

    .navbar-nav .open .dropdown-menu {
        position: absolute;
        float: left;
        width: 190px;
        right: 10px;
        left: auto;
        background-color: #fff;
        -webkit-box-shadow: 0 6px 12px rgba(0, 0, 0, .175);
        box-shadow: 0 6px 12px rgba(0, 0, 0, .175);
    }
}

@media (min-width: 979px) {
    ul.nav li.dropdown:hover > ul.dropdown-menu {
        display: block;
    }
}

.no-wrap {
    white-space: nowrap;
}

.form-control {
    border: 1px solid #ccc;
    border-radius: 1px;
    padding: 11px 15px;
    height: auto;
    box-shadow: inset 0 0px 0px rgba(0, 0, 0, 0);
}

.form-control:focus {
    border-color: #00BCBE;
    outline: 0;
    -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075), 0 0 8px rgba(102, 175, 233, 0);
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075), 0 0 8px rgba(102, 175, 233, 0);
}

.pages-header {
    padding: 50px 0 10px;
    margin-bottom: 0px;
}

.pages-header__noMargin {
    margin-bottom: 0px;
}

[data-toggle~="collapse"] {
    cursor: pointer;
}

.comma-seperated span:after {
    content: ", "
}

.comma-seperated span:last-of-type:after {
    content: ""
}

.vertical-seperator:after {
    content: " | ";
    padding: 0 3px;
}

.vertical-seperator:last-of-type:after {
    content: ""
}

.dashed-links:after {
    content: " - "
}

.dashed-links:last-of-type:after {
    content: ""
}

.sidebar-smallMargin {
    margin-top: 10px;
    margin-bottom: 0px;
}

.sidebar-smallMargin + .sidebar-smallMargin {
    margin-top: 0px;
}

.layout-page {
    background-color: #fff;
}

.layout-page__content {
    /*border-right: 1px solid #e9e9e9;*/
}

@media (max-width: 991px) {
    .layout-page__content {
        /*border-right: 0px solid #e9e9e9;*/
    }
}

.layout-page__sidebar {
}

.layout-page__section {
    padding: 1.2rem 0em;
    /*border-bottom: 1px solid #e9e9e9;*/
    position: relative;
}

.layout-page__sectionSmall {
    padding: 20px 0;
}

.layout-page__sectionSmall:first-child {
    padding: 0px 0 20px;
}

.layout-page__sectionSmall:last-child {
    padding: 20px 0 0px;
}

.layout-page__section--person {
    padding: 1.5rem 2.5rem 0;
    position: relative;
}

.extra-page__title {
    font-size: 16px;
    color: #00BCBE;
    line-height: 1.2;
    margin-bottom: 2px;
    display: inline-block;
    font-family: "PF BeauSans W15 SemiBold", "Roboto", Cambria, Georgia, serif;
}

.extra-page__title:hover {
    text-decoration: underline;
}

.extra-page__subtitle {
    font-size: 20px;
    margin-top: 2px;
}

/*.layout-page__section:last-child {*/
/*border-bottom: 0px solid #eee;*/
/*}*/

.layout-sidebar__section {
    padding: 12px 2rem 12px 1.7rem;
    /*border-bottom: 1px solid #e9e9e9;*/
    position: relative;
}

.layout-sidebar__section-nopr {
    padding: 0rem 0rem 1.2rem 1.7rem;
    /*border-bottom: 1px solid #e9e9e9;*/
}

.layout-sidebar__section-nopr-nobb {
    padding: 1.2rem 0rem 1.2rem 1.7rem;
}

@media (max-width: 767px) {
    .layout-sidebar__section-nopr {
        padding: 12px 0 12px 0;
    }
}

.section-small a:hover {
    text-decoration: underline;
}

.sidebar-menu, .layout-sidebar__section, .sidebar-menu__item {
    position: relative;
}

.sidebar-menu__item {
    padding: 2px 0;
}

.sidebar-menu__item a:hover {
    text-decoration: underline;
}

.layout-page__sidebar--marginTop {
    margin-top: 50px;
}

.section-small {
    padding: 6px 0;
}

.section-small__title {
    font-size: 12px;
    font-family: "PF BeauSans W15 Bold", "Roboto", Cambria, Georgia, serif;
}

.text-links a {
    color: #00BCBE;
}

.text-links a:hover {
    text-decoration: underline;
}

.share-page {

}

.panel {
    margin-bottom: 0px;
    background-color: #fff;
    border: 1px solid transparent;
    border-radius: 4px;
    -webkit-box-shadow: 0 1px 1px rgba(0, 0, 0, 0);
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0);
}

.btn {
    padding: 13px;
    background-color: #00BCBE;
    color: #fff;
    outline: 0 !important;
    border-radius: 2px;
}

.btn:focus {
    color: #fff;
    box-shadow: none;
}

.btn:hover {
    padding: 13px;
    background-color: #00a8aa;
    color: #fff;
}

.btn-hero {
    background-color: #00BCBE;
    color: #fff;
    display: inline-block;
    width: auto;
    border-radius: 4px;
    padding: 12px 22px;
    font-size: 16px;
}

.btn-hero:hover {
    border-radius: 4px;
    padding: 12px 22px;
    background-color: #00a8aa;
}

.btn-hero--transp {
    color: #00BCBE;
    border: 1px solid #00BCBE;
    background-color: transparent;
    display: inline-block;
    width: auto;
    border-radius: 4px;
    padding: 8px 22px;
}

.btn-hero--transp:hover {
    color: #00a8aa;
    border: 1px solid #00a8aa;
    background-color: transparent;
    display: inline-block;
    width: auto;
    border-radius: 4px;
    padding: 8px 22px;
}

.btn-submit {
    background-color: #00BCBE;
    color: #fff;
    display: block;
    width: 100%;
}

.btn-submit:hover {
    background-color: #00a8aa;
}

.btn-submit--secondary {
    background-color: transparent;
    color: #00BCBE;
    display: block;
    width: 100%;
    border: 1px solid #00BCBE;
}

.btn-submit--secondary:hover {
    color: #00a8aa;
    border: 1px solid #00a8aa;
    background-color: transparent;
}

.btn-submit--inline {
    display: inline-block;
    padding: 9px 30px !important;
    width: auto;
}

.btn-submit--small {
    padding: 7px 20px !important;
}

.btn-login {
    background-color: transparent;
    color: #00BCBE;
    display: block;
    width: 100%;
    border-radius: 4px;
    padding: 6px 15px;
    font-size: 14px;
    border: 1px solid #00BCBE;
}

.btn-login--inline {
    display: inline-block;
    width: auto;
}

.btn-login:hover, .btn-login:focus {
    border-radius: 4px;
    padding: 6px 15px;
    background-color: transparent;
    color: #00a8aa;
    border: 1px solid #00a8aa;
}

.error {
    font-size: 12px;
    color: #b92c3c
}

#imagelightbox-caption {
    background-color: transparent;
    color: #fff;
}

.imagelightbox-arrow {
    border: 0;
    outline: 0;
}

.imagelightbox-arrow {
    background-color: rgba(0, 0, 0, 0);
}

.imagelightbox-arrow:hover, .imagelightbox-arrow:focus {
    background-color: rgba(255, 255, 255, 0);
}

.imagelightbox-arrow:before {
    width: 0;
    height: 0;
    border: 0em solid transparent;
    font-family: 'FontAwesome';
    content: '\f105';
    font-size: 45px;
    display: inline-block;
    color: #fff;
    margin-bottom: -27px;
}

.imagelightbox-arrow-left:before {
    border-left: none;
    border-right-color: #fff;
    margin-left: -35px;
    content: '\f104';
}

.imagelightbox-arrow-right:before {
    margin-right: -2px;
}

.touchEnabled.js-carousel .owl-nav {
    display: none !important
}

.js-carousel .owl-prev {
    position: absolute;
    left: -40px;
    top: -40px;
    width: 40px;
    height: 100%;
    color: #000;
    opacity: 0.3;
    background-image: url(../../web-icons/ChevronLeft-128.png);
    background-size: contain;
    background-position: center;
    background-repeat: no-repeat;
}

.js-carousel .owl-next {
    position: absolute;
    right: -40px;
    top: -40px;
    height: 100%;
    width: 40px;
    color: #000;
    opacity: 0.3;
    background-image: url(../../web-icons/ChevronRight-128.png);
    background-size: contain;
    background-position: center;
    background-repeat: no-repeat;
}

.theater-slider .owl-prev {
    position: absolute;
    left: 20px;
    top: -10px;
    width: 40px;
    height: 100%;
    opacity: 0.2;
    background-image: url(../../web-icons/back-arrow-circular-symbol.svg);
    background-size: contain;
    background-position: center;
    background-repeat: no-repeat;
}

.theater-slider .owl-next {
    position: absolute;
    right: 20px;
    top: -10px;
    height: 100%;
    width: 40px;
    opacity: 0.2;
    background-image: url(../../web-icons/right-arrow-circular-button.svg);
    background-size: contain;
    background-position: center;
    background-repeat: no-repeat;
}

.theater-slider .owl-prev:hover, .theater-slider .owl-next:hover {
    opacity: 0.6
}

.container-narrow {
    max-width: 1000px;
}

input[type=text] {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
}

.collapse-show-desktop {
    display: block;
}

@media (max-width: 991px) {
    .collapse-show-desktop {
        display: none !important;
    }

    .collapse.in {
        display: block !important;
    }

    .sidebar-menu__item {
        display: inline-block;
        padding: 15px 7px 0px;
    }

    .layout-page__sidebar--marginTop {
        margin-top: 30px;
    }
}

@media (max-width: 767px) {
    .layout-page__section {
        padding: 13px 1.2rem;
    }

    .profilePage .layout-page__section {
        padding: 13px 0rem;
    }

    .layout-sidebar__section {
        padding: 1.2rem;
    }

    .layout-page__sidebar--marginTop {
        margin-top: 10px;
    }
}

@media (max-width: 991px) {
    .sidebar-menu__item {
        background-color: #fff;
        font-size: 15px;
        line-height: 1.2;
        font-family: "PF BeauSans W15 SemiBold", "Roboto", Cambria, Georgia, serif;
        display: block;
        padding: 1.2rem;
        border-bottom: 1px solid #eee;
    }

    .sidebar-menu__item:last-child {
        border-bottom: 0px solid #eee;
    }

    .sidebar-menu__item a {
        text-decoration: none !important
    }

    .layout-sidebar__section--submenu {
        padding: 0;
    }
}

@media (min-width: 768px) {
    .layout-page__content .collapse {
        display: block !important;
        height: auto !important;
    }
}

/* ==========================================================================
    Header
   ========================================================================== */
.hamburger {
    /*position: absolute !important;*/
    /*top: -1px;*/
    /*right: 5px;*/
    z-index: 5000;
    outline: 0;
    padding: 7px 5px 2px 15px;
}

@media (min-width: 769px) {
    .hamburger-cont {
        width: 70px;
    }

    .hamburger {
        /*top: 5px;*/
    }
}

.header {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    width: 100%;
    padding: 8px 20px;
    z-index: 1000;
}

.header-push {
    width: 40px;
}

.header__logo {
    font-size: 21px;
    font-family: "PF BeauSans W15 Bold", "Roboto", Cambria, Georgia, serif;
    font-family: "FatFrank Free", Georgia;
    /*text-shadow: 0 1px 0 rgba(255, 255, 255, 0);*/
    color: #fff;
    line-height: 1em;
    margin-right: 20px;
}

.header__logo img {
    width: 90px;
    margin: 0 20px 0 0;
}

.header__logo:hover {
    opacity: 0.8;
    color: #fff;
}

.alt_body .header {
    position: relative;
    background-color: #fff;
    width: 100%;
    padding: 8px 20px;
    box-shadow: rgba(0, 0, 0, 0.1) 0px 1px 5px;
}

.alt_body .header__logo {
    color: #222;
}

.navbar-nav > li {
    padding: 0px;
}

.navbar-nav > li > a {
    color: #fff;
    font-size: 14px;
    padding: 1px;
    border-bottom: 1px solid transparent;
}

.navbar-nav > li > a:hover {
    /*border-bottom: 1px solid #fff;*/
}

/*.navbar-nav {*/
/*    margin: 10px 0px;*/
/*}*/

.alt_body .navbar-nav > li > a {
    color: #3b4241;
}

.alt_body .navbar-nav > li > a:hover {
    /*border-bottom: 1px solid #3b4241;*/
}

.nav > li > a:focus, .nav > li > a:hover {
    text-decoration: none;
    background-color: transparent;
}

.nav .open > a, .nav .open > a:focus, .nav .open > a:hover {
    background-color: transparent;
    border-color: transparent;
}

.dropdown-menu {
    border: 0px solid rgba(0, 0, 0, .15);
    border-radius: 0px;
    padding: 5px 0;
    -webkit-box-shadow: 0 6px 19px rgba(0, 0, 0, 0.19);
    box-shadow: 0 6px 19px rgba(0, 0, 0, 0.19);
    min-width: 140px;
}

@media (max-width: 767px) {
    .alt_body .header {
        padding: 6px 8px;
    }

    .header__logo img {
        width: 80px;
        margin: 0 10px 0 0;
    }

    .header__logo {
        margin-right: 10px;
        font-size: 18px;
    }

    .hamburger {
        top: 8px;
        right: 10px;
        padding: 5px 8px 5px;
    }

    .header {
        padding: 10px 8px;
    }
}

/* ==========================================================================
    Header - Search
   ========================================================================== */
.header-search {
    border-left: 1px solid rgba(255, 255, 255, 0.3);
    padding-left: 10px;
}

.alt_body .header-search {
    border-left: 1px solid rgba(0, 0, 0, 0.1);
}

#header-search-form .table-cell {
    position: relative;
}

.header-search-button {
    font-size: 18px;
    color: rgb(187, 187, 187);
    background-color: transparent;
    border-width: 0px;
    border-style: initial;
    border-color: initial;
    border-image: initial;
    outline: 0px;
    position: absolute;
    top: 7px;
    left: 9px;
}

.header-search input:focus {
    background-color: rgba(255, 255, 255, 0.2) !important;
    color: #fff;
}

.header-search input::-webkit-input-placeholder { /* WebKit, Blink, Edge */
    color: rgba(255, 255, 255, 0.8);
    text-overflow: ellipsis;
}

.header-search input:-moz-placeholder { /* Mozilla Firefox 4 to 18 */
    color: rgba(255, 255, 255, 0.8);
    opacity: 1;
    text-overflow: ellipsis;
}

.header-search input::-moz-placeholder { /* Mozilla Firefox 19+ */
    color: rgba(255, 255, 255, 0.8);
    opacity: 1;
    text-overflow: ellipsis;
}

.header-search input:-ms-input-placeholder { /* Internet Explorer 10-11 */
    color: rgba(255, 255, 255, 0.8);
    text-overflow: ellipsis;
}

.header-search input::-ms-input-placeholder { /* Microsoft Edge */
    color: rgba(255, 255, 255, 0.8);
    text-overflow: ellipsis;
}

.dataTables_filter input::-webkit-input-placeholder { /* WebKit, Blink, Edge */
    color: #bbb;
    text-overflow: ellipsis;
}

.dataTables_filter input:-moz-placeholder { /* Mozilla Firefox 4 to 18 */
    color: #bbb;
    opacity: 1;
    text-overflow: ellipsis;
}

.dataTables_filter input::-moz-placeholder { /* Mozilla Firefox 19+ */
    color: #bbb;
    opacity: 1;
    text-overflow: ellipsis;
}

.dataTables_filter input:-ms-input-placeholder { /* Internet Explorer 10-11 */
    color: #bbb;
    text-overflow: ellipsis;
}

.dataTables_filter input::-ms-input-placeholder { /* Microsoft Edge */
    color: #bbb;
    text-overflow: ellipsis;
}

.alt_body .header-search input::-webkit-input-placeholder { /* WebKit, Blink, Edge */
    color: rgb(157, 157, 157)
}

.alt_body .header-search input:-moz-placeholder { /* Mozilla Firefox 4 to 18 */
    color: rgb(157, 157, 157);
    opacity: 1;
}

.alt_body .header-search input::-moz-placeholder { /* Mozilla Firefox 19+ */
    color: rgb(157, 157, 157);
    opacity: 1;
}

.alt_body .header-search input:-ms-input-placeholder { /* Internet Explorer 10-11 */
    color: rgb(157, 157, 157)
}

.alt_body .header-search input::-ms-input-placeholder { /* Microsoft Edge */
    color: rgb(157, 157, 157)
}

.header-search input {
    font-size: 14px;
    width: 350px;
    background-color: transparent;
    color: #fff;
    padding: 10px 10px 10px 32px;
    outline: none;
    border-width: 0px;
    border-style: initial;
    border-color: initial;
    border-image: initial;
}

.alt_body .header-search input {
    color: #222;
    width: 100%;
}

.autocomplete-suggestions {
    background: #FFF;
    overflow: auto;
    top: 55px !important;
    position: absolute !important;
    max-width: 348px;
}

.autocomplete-suggestion {
    overflow: hidden;
    border-bottom: 1px solid #f0f0f0;
    cursor: pointer
}

.autocomplete-selected {
    background: #F0F0F0;
    display: block;
}

.autocomplete-suggestions strong {
    font-weight: normal;
    color: #3399FF;
}

.autocomplete-suggestions .search-photo {
    margin-right: 10px;
}

.autocomplete-suggestions a {
    color: #000 !important;
    display: block;
}

.autocomplete-suggestions .table {
    margin: 0;
    width: 100%;
}

.autocomplete-suggestions .table .table-cell + .table-cell {
    width: 100%;
}

.autocomplete-suggestions .table-cell {
    padding: 0px 10px 0px 0;
    font-size: 14px;
    vertical-align: middle;
}

.autocomplete-suggestions .table-cell h6 {
    font-size: 14px;
}

.header-search-more {
    text-align: center;
    padding: 20px;
}

.header-search-more:hover {
    cursor: pointer;
    background-color: #eee;
}

.search-photo {
    width: 50px;
    height: 50px;
    background-size: cover;
    position: relative;
    background-color: rgb(238, 238, 238);
    background-position: center center;
}

.autocomplete-suggestions .search-photo {
    margin-right: 10px;
}

@media (max-width: 767px) {
    .header-search input {
        font-size: 12px;
        width: 100%;
    }

    .header-search-button {
        font-size: 14px;
    }

    .header-search {
        padding-left: 0px;
    }

    .autocomplete-suggestions {
        margin-top: 54px;
        border-top: 1px solid #eee;
        position: absolute;
        top: 0 !important;
        left: 0 !important;
        right: 0 !important;
        width: 100% !important;
    }

    .header-search-button {
    }

}

/* ==========================================================================
    Homepage - Hero
   ========================================================================== */
.hero {
    background-size: cover;
    background-position: center;
    height: 80vh;
}

.hero__opacity {
    background-color: rgba(0, 0, 0, 0.45);
    height: 80vh;
    position: relative;
    display: table;
    width: 100%;
}

.hero__details {
    color: #fff;
    width: 100%;
    text-align: center;
    display: table-cell;
    vertical-align: middle;
}

.hero__logo {
    font-size: 80px;
    opacity: 0.9;
    line-height: 1.2;
    letter-spacing: -0.04em;
}

.hero__logo img {
    width: 320px;
    max-width: 70vw;
}

.hero__logo > span {
    color: rgb(234, 6, 63);
}

.hero__logo > span + span {
    color: #fff;
}

.hero__title {
    font-weight: bold;
    font-size: 28px;
    padding: 0 20px;
}

.hero__subtitle {
    font-size: 16px;
    margin-top: 15px;
    margin-bottom: 5px;
    opacity: 0.86;
    padding: 0 20px;
}

.hero-actions .btn {
    margin: 30px 10px;
}

@media (max-width: 991px) {
    .hero__title {
        font-size: 30px;
    }

    .hero__subtitle {
        font-size: 14px;
    }
}

@media (max-width: 767px) {
    .hero__title {
        font-size: 24px;
    }

    .hero__subtitle {
        font-size: 14px;
    }
}

/* ==========================================================================
    Header - Mobile menu
   ========================================================================== */
@media (min-width: 768px) {
    .header-right {
        width: 260px;
    }
}

@media (max-width: 767px) {
    .nav > li {
        display: inline-block;
    }

    .header-right {
        padding-right: 10px;
    }
}

.mobile-menu li a {
    font-size: 1.7rem;
    padding: 10px 0;
    display: inline-block;
}

/* ==========================================================================
    Homepage - Search Bucket
   ========================================================================== */
.search-mob {
    position: relative;
    outline: 0 !important;
}

.search-mob__input {
    background-color: #00BCBE;
    line-height: 50px;
    padding: 0 20px 0 20px;
    display: inline-block;
    border: 1px solid #00BCBE;
    color: #fff;
    border-radius: 4px;
    margin-top: 30px;
    font-size: 15px;
    outline: 0 !important;
}

.search-mob i {
    font-size: 20px;
    margin-right: 5px;
    line-height: 50px;
    outline: 0;
    border: 0;
}

.search-mob .button:hover {
    display: inline-block;
    height: 50px;
    line-height: 50px;
    font-size: 22px;
    padding: 0 20px;
}

#mob-search .btn-full {
    width: 100%;
}

.search-bucket {
    background-color: #fff;
    padding: 30px 0;
}

.search-bucket__title {
    margin-bottom: 20px;
    font-size: 24px;
    color: #717171;
}

.search-bucket__dropdown {
    width: 260px;
    color: #555;
    border: 1px solid #ccc;
    background-color: transparent;
    margin: 0px;
    text-align: left;
    position: relative;
    font-size: 16px;
    outline: 0 !important;
}

.search-bucket__dropdown:focus {
    color: #444 !important;
}

.search-bucket__dropdown:hover {
    border: 1px solid #bbb;
    background-color: transparent;
    color: #444;
}

.search-bucket__dropdown span {
    width: auto;
    text-overflow: ellipsis;
}

.search-bucket__dropdown span + span {
    position: absolute;
    right: 15px;
    top: 20px;
    border-top: 6px dashed;
    border-right: 6px solid transparent;
    border-left: 6px solid transparent;
}

.search-bucket__submit, .search-bucket__submit:hover {
    font-size: 16px;
    padding-right: 30px;
    padding-left: 30px;
}

.search-bucket__submit--small, .search-bucket__submit--small:hover {
    font-size: 13px;
    padding: 7px 10px
}

.search-bucket-genre-select, .search-bucket-time-select {
    width: 100%;
}

.search-bucket-genre-select {
    height: 200px;
    overflow-y: scroll;
}

/* ==========================================================================
    Homepage - Carousel
   ========================================================================== */
.touchEnabled .owl-item.cloned {
    display: none !important;
}

.touchEnabled .owl-stage {
    transform: none !important;
    transition: none !important;
}

.touchEnabled .owl-item {
    width: auto !important;
}

.touchEnabled .owl-stage-outer {
    padding-bottom: 10px;
    overflow-x: scroll !important;
    -webkit-overflow-scrolling: touch;
}

.touchEnabled .owl-nav {
    display: none;
}

.homepage-teaser {
    background-color: #eee;
    padding: 50px 0 60px;
    text-align: center
}

.homepage-teaser__title {
    font-size: 24px;
}

.homepage-teaser__subtitle {
    font-size: 18px;
    opacity: 0.7;
    margin-bottom: 20px;
}

.homepage-interviews {
    background-color: #fbfbfb;
    padding: 50px 0 50px;
    margin: 30px 0 50px !important;
}

.homepage-carousel {
    margin: 30px 0;
}

.homepage-carousel__title {
    font-size: 20px;
    line-height: 1.2;
    margin-bottom: 15px;
}

.homepage-carousel__title--alt {
    font-size: 30px;
    font-weight: bold;
    line-height: 1.2;
    margin-bottom: 20px !important;

    letter-spacing: -0.02em;
}

.homepage-carousel__title--alt i {
    margin-left: 10px !important;
}

@media (max-width: 767px) {
    .homepage-carousel__title--alt {
        font-size: 24px;
    }
}

.person-section__link {
    display: flex;
    align-items: center;

}

.person-section__link span {
    color: #00BCBE;
    font-size: 13px;
    display: inline-block;
    margin-left: 10px;
    margin-top: 2px;
}

.person-section__link i {
    font-size: 24px;
    color: #00BCBE;
    margin-left: 4px;
}

.homepage-carousel__link {
    color: #00BCBE;
}

a.person-section__link:hover {
    color: #00BCBE;
    text-decoration: none;
}

.person-section__quote {
    font-size: 15px;
}

.homepage-carousel__link:hover {
    color: #00BCBE;
    text-decoration: underline;
}

.hero-link {
    position: absolute;
    right: 15px;
    bottom: 10px;
}

.hero-link a {
    color: rgba(255, 255, 255, 0.8);
    font-size: 12px;
}

.hero-link a:hover {
    color: rgba(255, 255, 255, 1);
}

.play-grid a {
    display: block;
}

.touchEnabled .play-grid {
    width: 300px;
    display: inline-block;
    white-space: normal;
    vertical-align: top;
}

.touchEnabled .trending-actors {
    display: inline-block;
    white-space: normal;
    vertical-align: top;
}

.touchEnabled {
    overflow-x: scroll;
    overflow-y: hidden;
    width: 100%;
    white-space: nowrap;
    -webkit-overflow-scrolling: touch;
}

.play-grid__image {
    height: 210px;
    background-size: cover;
    background-color: rgb(238, 238, 238);
    background-position: center center;
}

.play-grid__rating {
    border-radius: 4px;
    background-color: rgba(0, 0, 0, 0.7);
    font-size: 10px;
    display: inline-block;
    position: absolute;
    color: rgba(255, 255, 255, 0.91);
    left: 5px;
    top: 8px;
    padding: 5px 7px 4px;
}

.play-grid__rating .fa-star {
    color: #ffca00;
    padding-right: 1px;
    font-size: 12px;
}

.play-grid__rating .fa-comment-o {
    padding-left: 5px;
    padding-right: 1px;
    font-size: 12px;
}

@media (max-width: 767px) {
    .search-results-c .play-grid__rating {
        border-radius: 2px;
        background-color: rgba(0, 0, 0, 0.7);
        font-size: 11px;
        display: inline-block;
        position: absolute;
        color: rgba(255, 255, 255, 0.91);
        left: 0px;
        top: 0px;
        padding: 2px 5px;
    }

    .search-results-c .play-grid__rating .fa-star {
        color: #ffca00;
        padding-right: 1px;
        font-size: 9px;
    }
}

@media (min-width: 1600px) {
    .play-grid__image {
        height: 230px;
    }
}

@media (min-width: 2200px) {
    .play-grid__image {
        height: 260px;
    }
}

.play-grid__details {
    margin-top: 8px;
}

.play-grid__relative {
    position: relative;
}

@media screen and (min-width: 767px) {
    .play-grid__details {
        position: relative;
    }
}

.play-grid__title {
    font-size: 13px;
    font-family: "PF BeauSans W15 SemiBold", Roboto, Cambria, Georgia, serif;
    margin: 6px 28px 2px 0;
    color: #000;
}

.play-grid__info {
    font-size: 11px;
    color: rgb(153, 153, 153);
    margin: 5px 0 0;
}

.play-grid__date {
    font-size: 11px;
    color: rgb(96, 96, 96);
    margin: 6px 0 0;
}

.play-grid {
    margin-bottom: 40px;
    position: relative;
}

.play-grid--video {
    margin-bottom: 20px;
    position: relative;
}

@media (max-width: 1024px) {
    .js-carousel .play-grid {
        width: 310px;
    }

    .js-carousel .play-grid__image {
        width: 100%;
        height: 220px;
    }

    .homepage-carousel .container--noPadding {
        width: 100%;
    }

    .homepage-carousel .container--noPadding .col-xs-12 {
        padding-right: 0px;
        padding-left: 0px;
    }

    .touchEnabled .play-grid:first-child,
    .touchEnabled .trending-actors:first-child {
        display: inline-block;
        margin-left: 15px;
    }

    .touchEnabled .play-grid:last-child,
    .touchEnabled .trending-actors:last-child .trending-actors {
        margin-right: 15px;
    }

}

@media (max-width: 767px) {
    .homepage-carousel__title {
        margin-bottom: 3px;
    }

    .homepage-carousel__link {
        margin-bottom: 15px;
        display: inline-block;
    }

    .play-grid {
        margin-bottom: 0px;
    }

    .search-results-c .play-grid {
        display: table;
        border-bottom: 1px solid #eee;
        width: 100%;
    }

    .search-results-c .play-grid__image {
        display: table-cell;
        width: 80px;
        height: 60px;
    }

    .search-results-c .play-grid__details {
        padding-left: 20px;
        padding-right: 40px;
        display: table-cell;
        vertical-align: middle;
    }
}

@media (min-width: 1600px) {
    .homepage-carousel .container,
    .container-limit {
        width: 1550px;
    }
}

/* ==========================================================================
    Homepage - Actors
   ========================================================================== */
.trending-actors {
}

.trending-actors__photo {
    width: 100%;
    height: 170px;
    background-size: cover;
    background-position: center;
}

.trending-actors__name {
    font-size: 12px;
    padding-top: 6px;
}

.touchEnabled .trending-actors {
    width: 130px;
    display: inline-block;
}

/* ==========================================================================
    Play - Hero
   ========================================================================== */
.open-images {
    cursor: pointer;
}

.play-hero {
    height: 60vh;
    background-size: cover;
    background-position: center;
    display: table;
    width: 100%;
    min-height: 230px;
    background-color: #ccc;
}

.play-hero__photoLink {
    /*float: right;*/
    font-size: 12px;
}

.play-hero__photoLink:hover {
    color: #fff;
}

.play-hero__photoLink i {
    opacity: 0.9;
    padding-left: 1px;
    font-size: 11px;
}

.play-hero.b-lazy {
    opacity: 1 !important;
}

.play-hero--noimage {
    height: 40vh;
}

.play-hero__bottom {
    display: table-cell;
    vertical-align: bottom;
}

.play-hero__info {
    background-color: rgba(0, 0, 0, 0.8);
    padding: 2rem 2rem 2rem 2.5rem;
}

.play-hero__title {
    font-size: 1.6rem;
    line-height: 1.3;
    color: #fff;
    font-family: "PF BeauSans W15 Bold", "Roboto", Cambria, Georgia, serif;
    margin-bottom: 10px;
}

.play-hero__theater {
    color: #fff;
    font-size: 14px;
    margin-top: 10px;
}

.play-hero__moreinfo {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.8);
    margin-top: 10px;
}

@media (max-width: 990px) {

    .play-hero__title {
        font-size: 1.5rem;
    }
}

@media (max-width: 767px) {
    .play-hero__info {
        padding: 1rem 1.5rem 1rem 1.5rem;
    }

    .play-hero__title {
        font-size: 1.5rem;
    }

    .play-hero {
        height: 70vh;
    }
}

/* ==========================================================================
    Play - Content
   ========================================================================== */
.play-persons {
    position: relative;
}

.collapse-title, .play-persons__title {
    font-size: 15px;
    line-height: 1.2;
    font-family: "PF BeauSans W15 SemiBold", "Roboto", Cambria, Georgia, serif;
    margin-bottom: 5px;
}

.collapse-title {
    /*display:none;*/
}

.collapse-title__hidden {
    display: none;
}

.play-actors {
    padding-top: 11px;
}

.play-actors a:hover .play-actors__name {
    text-decoration: underline;
}

@media (min-width: 1025px) {
    .play-actors .follow-person {
        display: none
    }

    .play-actors .follow-button--attached {
        display: block
    }
}

.play-actors:hover .follow-person {
    display: block
}

.play-sintelestes {
    padding-bottom: 13px;
}

.play-sintelestes__title {
    font-family: "PF BeauSans W15 SemiBold", "Roboto", Cambria, Georgia, serif;
    margin-bottom: 4px;
    font-size: 13px;
}

.play-sintelestes__person {
    font-size: 12px;
}

.play-sintelestes__person + .play-sintelestes__person {
    margin-top: 5px;
}

.play-sintelestes__person a:hover {
    text-decoration: underline;
}

.play-sidebar__theater {
    font-size: 14px;
    font-family: "PF BeauSans W15 SemiBold", "Roboto", Cambria, Georgia, serif;
}

.play-map {
    width: 100%;
    height: 400px;
    margin-top: 5px;
    border: 2px solid #fff;
    background-color: #fff;
}

.play-sidebar__contact i {
    margin-right: 7px;
    width: 10px;
}

.play-sidebar__theater a:hover {
    text-decoration: underline;
}

.user-comment__hero {
    padding: 2.5rem 2.5rem 2.5rem 0;
    position: relative;
}

.user-comment {
    padding: 15px 0px 15px 0;
    background-color: #fff
}

.play-reviews div.user-comment:first-child {
    padding: 0px 0px 15px 0;

}

.play-reviews div.user-comment:last-child {
    padding: 15px 0px 0px 0;
}

.user-comments__cont {
    background-color: #fff
}

.user-comments__cont--create {
    padding: 30px 30px;
    background-color: #fff
}

.user-comments__cont .user-comment {
    padding: 30px 30px 20px;
}

.user-comments__cont--create .search-bucket__submit--small {
    margin-top: 10px;
}

.user-comment {
    border-bottom: 5px solid #f4f4f4;
}

.play-reviews .user-comment {
    border-bottom: 1px solid #efefef;
}

.play-reviews .user-comment:last-child {
    border-bottom: 0px solid #efefef;
}

.person-section__title {
    font-size: 22px;
    display: inline-block;
    line-height: 1.2;
    margin-bottom: 14px;
    font-family: "PF BeauSans W15 SemiBold", "Roboto", Cambria, Georgia, serif;
}

.user-review__title {
    font-size: 15px;
    line-height: 1.2;
    margin-bottom: 12px;
    font-family: "PF BeauSans W15 SemiBold", "Roboto", Cambria, Georgia, serif;
}

.user-comment__photo {
    background-size: cover;
    background-position: center;
    margin-right: 10px;
}

.user-comment__person {
    font-size: 13px;
    color: #000;
    font-family: "PF BeauSans W15 SemiBold", "Roboto", Cambria, Georgia, serif;
    margin-bottom: 3px;
}

.user-comment__text {
    padding: 0px 0;
    margin: 5px 0;
}

.user-comment__actions--small {
    font-size: 13px;
}

.user-comment__actions--margin {
    margin-bottom: 10px;
}

.user-comment__actions, .general-actions {
    text-align: right;
    margin-top: 10px;
}

.user-comment__actions--me {
    color: #8d8d8d
}

.user-comment__actions--me .fa {
    margin-right: 3px;
}

.user-comment__actions a, .general-actions a {
    color: #00BCBE;
    margin: 0 0 0 5px;
}

.user-comment__actions a:hover, .general-actions a:hover {
    text-decoration: underline;
}

.user-comment__textarea textarea {
    box-sizing: border-box;
    resize: none;
    border: 1px solid #ccc;
    box-shadow: none;
}

.play-calendar {
    width: 100%;
    font-size: 13px;
    color: #444;
    margin-bottom: 10px;
}

.play-calendar .deactivated {
    color: #bbb;
}

.play-calendar .main-time td {
    border-top: 1px dotted #ccc;
    padding: 5px 0;
    font-size: 12px;
}

.play-calendar td + td {
    text-align: right;
}

.play-calendar tr:first-child td {
    border-top: 0
}

.play-calendar .excemption-time td {
    padding: 4px 0;
    font-size: 11px;
}

.play-calendar .main-time + .excemption-time td {
    padding: 0px 0 4px 0;
}

.play-actors__role {
    font-size: 13px;
    opacity: 0.6;
    margin-top: 3px;
    font-style: italic;
}

@media (max-width: 991px) {
    .play-actors__name {
        font-size: 14px;
    }

    .play-persons__title {
        font-size: 12px;
    }

    .collapse-title__hidden {
        display: block;
    }

    .collapse-title, .play-persons__title {
        margin-bottom: 0px;
    }

    .collapse-title::after {
        font-family: FontAwesome;
        content: "\f106";
        position: absolute;
        color: #aaa;
        font-size: 22px;
        right: 20px;
        top: 10px;
    }

    .collapse-title.collapsed::after,
    .sidebar-menu__item a::after {
        font-family: FontAwesome;
        content: "\f105";
        position: absolute;
        color: #aaa;
        font-size: 22px;
        right: 20px;
        top: 10px;
    }

    .sidebar-menu__item {
        padding: 0;
    }

    .sidebar-menu__item a {
        display: block;
        padding: 1.2rem;
    }

    .collapse-title__noIcon::after {
        content: "";
    }

    .collapse-padding {
        margin-top: 18px;
    }

    #move-sidebar {
        padding: 0;
        border-bottom: 0px solid #eee;
    }

}

/* ==========================================================================
    Person - Content
   ========================================================================== */
.person-margin-top {
    margin-top: 10px;
}

.person-follow {
    padding: 15px 0;
}

#person-profile::after {
    content: "";
    background-size: cover;
    opacity: 0.7;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    position: absolute;
    z-index: -1;
    -webkit-filter: grayscale(1) blur(12px); /* Old WebKit */
    filter: grayscale(1) blur(12px);
    overflow: hidden;
}

.person-hero {
    background-color: rgba(13, 13, 13, 0.85);
    position: relative;
    padding: 2rem 0px 2rem;
    overflow: hidden;
}

.person-hero__photo {
    width: 190px;
    object-fit: cover;
    height: 230px;
    margin-right: 15px;
}

.play-hero__photo {
    width: 65px;
    border-radius: 3px;
    margin-right: 5px;
}

.person-hero__name {
    font-size: 30px;
    color: #fff;
    line-height: 38px;
    font-family: "PF BeauSans W15 SemiBold", "Roboto", Cambria, Georgia, serif;
}

.person-hero__text {
    font-size: 14px;
    margin-top: 10px;
    color: rgba(255, 255, 255, 0.85);
    max-height: 150px;
    overflow: hidden;
}

.person-hero__roles {
    font-size: 14px;
    color: #fff;
    opacity: 0.6
}

.person-hero__plays {
    color: #000;
    background-color: rgba(0, 188, 190, 0.14);
    border-left: 3px solid #00bcbe52;
    padding: 10px 10px;
}

.person-hero__plays a {
    color: #007071;
}

.person-hero__plays a:hover {
    text-decoration: underline;
}

.person-hero__links {
    position: absolute;
    top: 0;
    right: 0;
    text-align: right;
}

@media (max-width: 991px) {
    .person-hero__links {
        position: relative;
        margin-top: 20px;
        text-align: left;
    }
}

@media (max-width: 767px) {
    .person-hero__links {
        position: relative;
        margin-top: 20px;
    }

    .person-hero__text {
        margin-top: 0px;
    }

}

.person-hero__links a {
    margin-right: 5px;
    font-size: 18px;
    color: #fff;
    opacity: 0.7
}

.person-hero__links a:hover {
    text-decoration: none;
    opacity: 1
}

.playPhoto {
    width: 100%;
    padding-top: 60%;
    position: relative;
    overflow: hidden;
    height: auto !important;
}

.playPhoto img {
    object-fit: cover;
    object-position: center;
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    transition: transform .5s ease;
}

.personMainPhoto {
    width: 140px;
    padding-top: 110%;
    position: relative;
    margin-right: 15px;
    overflow: hidden;
    height: auto !important;
}

.moviesMainPhoto {
    width: 110px;
    padding-top: 60%;
    position: relative;
    margin-right: 5px;
    overflow: hidden;
    height: auto !important;
}

@media (max-width: 767px) {
    .personMainPhoto {
        width: 115px;
    }

    .moviesMainPhoto {
        width: 80px;
    }
}

.personMainPhoto img, .moviesMainPhoto img {
    object-fit: cover;
    object-position: center;
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    transition: transform .5s ease;
}

.no-gutter {
    margin-right: -2px;
    margin-left: -2px;
}

.no-gutter > [class*="col-"] {
    padding-right: 2px;
    padding-left: 2px;
}

.person-hero--min {
    position: relative;
    margin-top: 10px;
}

.person-hero--min .personMainPhoto {
    border-radius: 3px;
    width: 85px;
    margin-right: 5px;
}

.person-play {
    padding: 14px 0;
    position: relative;
    border-bottom: 1px dotted rgba(0, 0, 0, 0.1);
}

.person-play:last-child {
    border-bottom: 0px dotted rgba(0, 0, 0, 0.1);
}

.person-play__title a {
    font-size: 16px;
    line-height: 1.6;
    color: #000;
}

.person-play__title a:hover {
    text-decoration: underline;
}

.person-play__theater, .person-play__theater a {
    color: #777;
    font-size: 12px;
}

.person-play__theater a:hover {
    text-decoration: underline;
}

.person-play__year {
    color: #999;
    font-size: 11px;
}

.person-trendingPlay {
    margin: 13px 0;
}

.person-trendingPlay + .person-trendingPlay {
    margin: 13px 0 0;
}

.person-trendingPlay__photo {
    width: 50px;
    height: 40px;
    background-size: cover;
    margin-right: 10px;
    border-radius: 3px;
    transition: opacity 0.4s ease;
}

.person-trendingPlay__role {
    color: #B4B4B4;
    font-size: .9rem;
    line-height: 1.7;
}

@media (max-width: 1199px) and (min-width: 768px) {
    .play-grid--margin {
        margin-top: 10px;
    }
}

.person-trendingActor {
    margin: 13px 0;
}

.person-trendingActor + .person-trendingActor {
    margin: 13px 0 0;
}

.person-trendingActor__photo {
    width: 40px;
    height: 40px;
    background-size: cover;
    margin-right: 10px;
    border-radius: 3px;
    transition: opacity 0.4s ease;
}

.person-trendingActor__play {
    color: #B4B4B4;
    font-size: .9rem;
    line-height: 1.7;
}

.quote__actions, .trivia__actions {
    padding-top: 4px;
}

.quote__actions a, .trivia__actions a {
    color: #00BCBE;
    font-size: 14px;
}

.quote__actions a:hover, .trivia__actions a:hover {
    text-decoration: underline;
}

.quote__actions--min {
    margin-top: 15px;
}

.person-contribution {
    background-color: #674386;
    padding: 20px;
}

.person-contribution:hover {
    background-color: #5a3a75;
}

.person-contribution__title {
    color: #fff;
    font-size: 18px;
    font-family: "PF BeauSans W15 Bold", "Roboto", Cambria, Georgia, serif;
}

.person-contribution__text {
    color: #fff;
    font-size: 15px;
    margin-top: 10px;
}

.person-contribution_link {
    width: 100%;
    text-align: left;
    padding: 0;
    font-size: 14px;
    color: #fff;
    outline: 0;
    border: 0;
}

.person-contribution_link:hover,
.person-contribution_link:focus {
    color: #fff;
    text-decoration: none !important;
}

.person-pro {
    margin-left: 10px;
}

.person-pro img {
    width: 18px;
    margin-top: -10px;
}

.person-gallery__title {
    font-family: "PF BeauSans W15 SemiBold", "Roboto", Cambria, Georgia, serif;
    margin-bottom: 10px;
}

.person-gallery__photoCont {
    width: 100%;
    padding-top: 70%;
    position: relative;
    overflow: hidden;
    height: auto !important;
}

.person-gallery__photo {
    object-fit: cover;
    object-position: center;
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    transition: transform .5s ease;
}

.quotes_item {
    padding: 5px 0;
}

@media (max-width: 767px) {
    .person-hero__photo {
        width: 140px;
    }

    .person-hero__name {
        font-size: 24px;
    }

    /*.person-gallery__photo {*/
    /*    height: 110px;*/
    /*}*/
}

@media (max-width: 479px) {
    /*.person-gallery__photo {*/
    /*    height: 90px;*/
    /*}*/
}

@media (max-width: 991px) {
    /*.person-hero {*/
    /*    padding: 3rem 0px 3rem;*/
    /*}*/
    /*.person-margin-top {*/
    /*    margin-top: -5px;*/
    /*}*/
}

/* ==========================================================================
    Plays - Index
   ========================================================================== */

.plays-header {
    padding: 20px 0;
    margin-bottom: 20px;
}

.plays-header__title {
    font-size: 14px;
}

.plays-header__subtitle {
    font-size: 13px;
}

.filters-button, #close-filters {
    display: none;
}

.filters-button {
    margin: 0px auto 20px;
    border: 1px solid #00BCBE;
    padding: 9px;
    color: #00BCBE;
    text-align: center;
    background-color: transparent;
    outline: 0;
    border-radius: 2px;

}

.search-results-sidebar .panel-body {
    padding: 15px 15px 15px 0;
}

.search-results-sidebar .panel {
    background: transparent;
}

.panel-heading-filters {

}

.collapse-title-filters::after {
    font-family: FontAwesome;
    content: "";
    position: absolute;
    color: rgb(51, 51, 51);
    font-size: 22px;
    right: 20px;
    top: 10px;
}

.collapse-title-filters.collapsed::after {
    content: "";
}

.panel-heading-filters {
    position: relative;
    font-size: 14px;
    display: block;
    border-bottom: 1px solid rgb(225, 225, 225);
}

.panel-heading-filters a {
    display: block;
    padding: 15px 0px;
}

.search-results-sidebar .panel-collapse span {
    float: right;
    background-color: rgb(244, 244, 244);
    color: #888;
    font-size: 10px;
    padding: 2px 6px;
    border-radius: 8px;
    border: 1px solid #ccc;
}

.search-results-sidebar ul li a {
    display: block;
}

.search-results-sidebar ul li a, .select-period {
    color: rgb(144, 144, 144);
    padding: 4px;
    font-size: 13px;
}

.search-results-sidebar ul li a:hover, .select-period:hover {
    text-decoration: underline;
    color: #00BCBE;
}

.search-results-sidebar ul li.active a, .select-period.active {
    color: #00BCBE;
}

.search-results-sidebar ul li.active span {
    border-color: #00BCBE;
    color: #00BCBE;
}

.sr-tag span {
    display: inline-block;
    vertical-align: middle;
}

.sr-tag {
    display: inline-block;
    zoom: 1;
    color: rgb(85, 85, 85);
    vertical-align: middle;
    line-height: 12px;
    font-size: 12px;
    font-weight: 400;
    text-align: center;
    margin-bottom: 10px;
    border-width: 1px;
    border-style: solid;
    border-color: rgb(210, 214, 223);
    border-image: initial;
    padding: 2px 6px 2px 10px;
    border-radius: 15px;
}

.sr-tag:hover {
    border-color: #00BCBE;
    color: #00BCBE;
}

.sr-tag i {
    font-size: 24px;
    vertical-align: middle;
}

.sr-tag-clear {
    display: inline-block;
    zoom: 1;
    color: rgb(85, 85, 85);
    vertical-align: middle;
    line-height: 12px;
    font-size: 12px;
    font-weight: 400;
    text-align: center;
    margin-bottom: 10px;
    border-width: 1px;
    border-style: solid;
    border-color: transparent;
    border-image: initial;
    padding: 7px 10px 6px;
}

.sr-tag-clear:hover {
    color: #00BCBE
}

.filter-dates-cont .form-group {
    margin-bottom: 5px;
}

.filter-dates-cont .form-control {
    border: 1px solid #ccc;
    border-radius: 1px;
    padding: 12px 10px;
    font-size: 12px;
}

.filter-dates-cont .btn {
    padding: 10px;
    border: 1px solid #00BCBE;
    background-color: transparent;
    color: #00BCBE;
}

@media (max-width: 990px) {
    .search-results-sidebar {
        display: none;
        position: fixed;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;
        background-color: #fff;
        z-index: 5001;
        padding: 50px 20px;
        overflow: scroll;
        -webkit-overflow-scrolling: touch;

    }

    .sr-tags {
        margin-bottom: 10px;
    }

    .filters-button {
        display: block;
        margin: 0px;
        border: 0px solid #00BCBE;
        padding: 15px 12px;
        color: #fff;
        font-size: 15px;
        background-color: rgba(0, 188, 190, 0.97);
        text-align: center;
        outline: 0;
        border-radius: 0px;
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        z-index: 1000;

    }

    #close-filters {
        display: inline-block;
        float: right;
        font-size: 50px;
        margin-top: -50px;
        margin-right: -10px;
    }
}

@media (min-width: 1500px) {
    .search-results-sidebar {
        padding-right: 40px;
    }
}

/* ==========================================================================
    Footer
   ========================================================================== */
.footer {
    padding: 50px 0 40px;
}

.footer a {
    color: #555;
    line-height: 2
}

.footer a:hover {
    text-decoration: underline;
}

.history-item {
    width: 40px;
    height: 40px;
}

.history-item.b-loaded {
    filter: grayscale(1);
    opacity: 0.8;
    border-radius: 2px;
}

.history-item.b-loaded:hover {
    filter: grayscale(0);
    opacity: 1;
}

.b-lazy {
    -webkit-transition: opacity 500ms ease-in-out;
    -moz-transition: opacity 500ms ease-in-out;
    -o-transition: opacity 500ms ease-in-out;
    transition: opacity 500ms ease-in-out;
    max-width: 100%;
    opacity: 0;
}

.b-lazy.b-error {
    opacity: 1;
    background-color: #eee !important;
}

.b-lazy.b-loaded {
    opacity: 1;
}

.footer-links {
    padding-top: 20px;
    font-size: 13px;
}

.footer-links li:after {
    content: '|';
    color: #ccc;
    display: inline-block;
    padding-left: 20px;
}

.footer-links li:last-child:after {
    content: '';
}

.copyright {
    font-size: 12px;
    color: #666;
}

/* ==========================================================================
    Festivals
   ========================================================================== */
.festivals {
}

.festival-page__nav {
    margin: 30px 0 0;

}

.festivals_header {
    margin-bottom: 10px;
}

.festivals__title {
    font-size: 28px;
    margin-bottom: 6px;
    letter-spacing: -0.03em;
}

.festivals__subtitle {
    font-size: 16px;
    color: #a9a9a9;
    margin-bottom: 30px;
}

.festival-item {
    border-bottom: 1px solid #eee;
    padding: 17px 0;
}

.festival-item__title {
    font-size: 16px;
}

.festival-item__title a:hover {
    text-decoration: underline;
}

.festival-item__date {
    color: #999;
}

.festival-plays {
    display: table;
    width: 100%;
}

.media__noMargin {
    margin: 0;
}

.festival-plays__row {
    display: table-row;
}

.festival-plays__cell {
    display: table-cell;
    font-size: 13px;
    vertical-align: middle;
    height: 70px;
}

.festival-plays__cellPadding {
    padding: 0px 15px;
    min-width: 200px;
    text-overflow: ellipsis;
}

.festival-plays__rowAlt {
    background-color: #fff;
}

.festival-plays__cellImage {
    background-color: #eee;
    padding: 0;
    width: 100px;
    height: 70px;
}

.festival-plays__cellImage img {
    width: 100px;
    height: 70px;
    object-fit: cover;
    object-position: center;
}

.select-genre-dropdown {
    text-align: right;
    padding-top: 5px;
}

.dropdown-menu-genre {
    max-width: 300px;
}

.dropdown-menu-genre > li > a {
    white-space: normal;

}

#select-theater {
    padding: 10px 15px;
    border: 1px solid #ddd !important;
    border-radius: 15px;
    color: rgb(85, 85, 85);
    vertical-align: middle;
    line-height: 12px;
    font-size: 12px;
    text-overflow: ellipsis;
    max-width: 80%;
    display: inline-block;
    white-space: nowrap;
    overflow: hidden;
}

#select-theater:hover {
    border: 1px solid #00BCBE !important;
    color: #00BCBE
}

@media (max-width: 767px) {
    .festival-item__title {
        margin-bottom: 10px;
    }

    .festival-plays__cell {
        display: block;
        height: auto;
    }

    .festival-plays__cell:first-child {
        padding-top: 8px;
        font-size: 11px;
        padding-bottom: 3px;
        color: #999;
    }

    .festival-plays__cell:last-child {
        padding-bottom: 8px;
    }

    .festival-plays__cellPadding {
        padding: 1px 5px;
    }

    .festival-plays__cellImage {
        width: 120px;
        height: 100px;
    }

    .festival-plays__cellImage img {
        width: 120px;
        height: 100px;
    }

    .select-genre-dropdown {
        text-align: left;
        padding-top: 0px;
    }

    .dropdown-menu-right {
        left: 0;
        right: auto;
    }
}

/* ==========================================================================
    Contributions
   ========================================================================== */
.contributions {
}

.contributions__header {
    margin: 20px 0;
}

.contributions__title {
    font-size: 24px;
    margin-bottom: 20px;
}

.contributions__subtitle {
    font-size: 13px;
}

.contributions-form__title {
    font-size: 18px;
    margin-bottom: 20px;
}

.contributions-form__section {
    background-color: #fff;
    padding: 25px;
    margin-bottom: 10px;
}

.contributions-form__info {
    font-size: 12px;
    margin-top: 10px;
}

/* ==========================================================================
    People
   ========================================================================== */
.people {
}

.people-index {
    padding: 30px 0;
    margin-bottom: 0px;
}

.people-index__title {
    font-size: 22px;
    margin-bottom: 20px;
}

.people__header {
    padding: 20px 0;
    margin: 20px 0 0px;
}

.people__title {
    font-size: 24px;
    margin-bottom: 0px;
}

.people__subtitle {
    font-size: 13px;
}

.people__letters a {
    white-space: nowrap;
    font-size: 13px;
    border-radius: 5px !important;
    display: inline-block;
    padding: 5px 7px;
    text-align: center;
    border: 1px solid transparent;
}

.people__letters.active a, .people__letters a:hover {
    border: 1px solid #00BCBE;
    color: #00BCBE;
}

.people-item {
    padding: 10px 0;
    border-bottom: 1px solid #eee;
}

.people-item__alt {
    padding: 10px;
    border-bottom: 0px solid #eee;
    background-color: #fefefe;
    margin-bottom: 2px;
}

.people-item__details {
    padding-left: 20px;
}

.people-item__details + .people-item__details {
    padding-bottom: 20px;
}

.people-item__name a {
    font-size: 14px;
}

.people-item__name a:hover {
    text-decoration: underline;
}

.people-item__play a {
    display: inline-block;
    padding-top: 10px;
    font-size: 13px;
    color: #00BCBE;
}

.people-item__play a:hover {
    text-decoration: underline;
}

.people-item__theater {
    color: #999;
    font-size: 12px;
}

.people-item__theater:hover {
    text-decoration: underline;
}

.person-role-list {
    font-size: 13px;
    padding: 2px;
}

.person-role-list a:hover {
    text-decoration: underline;
}

#people-index-roles {
    padding: 0 0 20px 0;
}

@media (max-width: 767px) {
    .people-index {
        padding: 20px 0;
        margin-bottom: 0px;
    }

    #people-index-roles {
        display: none;
    }
}

/* ==========================================================================
   Interviews
========================================================================== */
.interview-listItem {
    margin-bottom: 15px;
}

.interview-listItem:last-child {
    margin-bottom: 0px;
}

.interview-listItem h4 {
    font-family: "PF BeauSans W15 SemiBold", "Roboto", Cambria, Georgia, serif;
    font-size: 14px;
    margin-bottom: 5px;
    line-height: 1.4em
}

.interview-listItem .interview-listItem__author {
    font-size: 12px;
}

.interview-sidebar {
    padding-left: 30px;
}

.interview-sidebar__sep {
    padding: 20px 0;
    border-bottom: 1px solid #cacaca;
}

.interview-sidebar__sep:last-child {
    border-bottom: 0px solid #cacaca;
}

.interview-sidebar-play__cont:hover .interview-sidebar-play__title {
    text-decoration: underline;
}

.interview-sidebar-play__title {
    font-size: 15px;
    font-family: "PF BeauSans W15 SemiBold", Roboto, Cambria, Georgia, serif;
    color: #000;
}

.interview-sidebar-play__info {
    font-size: 13px;
    color: rgb(153, 153, 153);
    margin: 5px 0 0;
}

.interview-sidebar-play__date {
    font-size: 13px;
    color: rgb(96, 96, 96);
    margin: 6px 0 0;
}

.interview-sidebar-play-cont {
    padding-bottom: 20px;
}

.interview-sidebar-play-cont:last-child {
    padding-bottom: 0px;
}

.interview-sidebar-theater-cont {
    padding-bottom: 30px;
}

.interview-sidebar-theater-cont:last-child {
    padding-bottom: 0px;
}

.gallery-inline {
    padding: 20px 0 5px;
}

.gallery-inline img {
    object-fit: cover;
    width: 100%;
    height: 230px;
}

.interview__title {
    font-size: 32px;
    margin-bottom: 20px;
    font-family: "PF BeauSans W15 SemiBold", "Roboto", Cambria, Georgia, serif;
}

.interview-item {
    background-color: #fff;
    margin-bottom: 5px;
}

.news-item {
    padding: 30px 20px 20px;
    background-color: #fff;
    margin-bottom: 5px;
}

.interview-item__photo {
    width: 400px;
    height: 280px;
    background-size: cover;
    background-position: center;
}

.interview-related {
    margin-top: 60px;
}

.interview-related__title {
    font-size: 20px;
    line-height: 1.2;
    margin-bottom: 15px;
}

.interview-related-item__photo {
    width: 100%;
    height: 250px;
    background-size: cover;
    background-position: center;
    background-color: #EEEEEE
}

.bio-interview {
    margin-bottom: 15px;
}

.bio-interview-item__photo {
    width: 130px;
    height: 90px;
    background-size: cover;
    background-position: center;
}

.bio-interview-item__details {
    padding: 0px 20px;
}

.bio-interview-item__title {
    font-size: 15px;
    margin-bottom: 12px;
    line-height: 1.2;
    font-family: "PF BeauSans W15 SemiBold", "Roboto", Cambria, Georgia, serif;
}

@media screen and (max-width: 1199px) {
    .interview-related-item__photo {
        height: 200px;
    }

    .interview-sidebar {
        padding-left: 10px;
    }
}

@media screen and (max-width: 991px) {
    .bio-interview-item__details {
        padding: 0px 0 0 10px;
    }

    .bio-interview-item__title {
        font-size: 12px;
    }

    .bio-interview-item__photo {
        width: 100px;
        height: 70px;
        background-size: cover;
        background-position: center;
    }

    .interview-related-item__photo {
        height: 220px;
    }

    .interview-sidebar {
        padding-left: 0px;
        padding-top: 40px;
    }

    .interview-related-item {
        margin-bottom: 30px;
    }
}

@media screen and (max-width: 767px) {
    .interview-related-item__photo {
        height: 60vw;
    }

    .interview-related-item {
        margin-bottom: 40px;
    }
}

.interview-related-item__title {
    font-size: 17px;
    margin-bottom: 10px;
    margin-top: 20px;
    font-family: "PF BeauSans W15 SemiBold", "Roboto", Cambria, Georgia, serif;
}

.interview-related-item__text {
    font-size: 14px;
    opacity: 0.9;
    line-height: 1.5;
    font-family: 'PF BeauSans W15 Light Italic', "Roboto", Cambria, Georgia, serif;
    padding-right: 20px;
}

.interview-item__photo--homepage {
    width: 350px;
    height: 220px;
    background-size: cover;
    background-position: center;
}

@media screen and (max-width: 767px) {
    .interview-item__photo--homepage {
        margin-bottom: 20px;
    }
}

.news-item__cont {
    float: left;
}

.news-item__photo {
    width: 300px;
    height: 210px;
    background-size: cover;
    background-position: center;
    margin-right: 30px;
    margin-bottom: 10px;
}

.news-item__photo--small {
    width: 200px;
    height: 140px;
}

.interview-item__details {
    padding: 30px 30px;
}

.news-item__details {
    padding: 0px 30px 0px 0px;
}

.interview-item__title {
    font-size: 18px;
    margin-bottom: 20px;
    font-family: "PF BeauSans W15 SemiBold", "Roboto", Cambria, Georgia, serif;
}

.interview-item__title--small {
    font-size: 17px;
    margin-bottom: 15px;
    font-family: "PF BeauSans W15 SemiBold", "Roboto", Cambria, Georgia, serif;
}

.news-item__title {
    font-size: 20px;
    font-family: "PF BeauSans W15 SemiBold", "Roboto", Cambria, Georgia, serif;
}

.news-item__header {
    border-bottom: 1px solid #eee;
    margin-bottom: 20px;
    padding-bottom: 15px;
}

.news-item__date {
    opacity: 0.6;
    font-size: 12px;
    padding-left: 20px;
}

.interview-item__text {
    font-size: 15px;
    opacity: 0.9;
    line-height: 1.5;
    font-family: 'PF BeauSans W15 Light Italic', "Roboto", Cambria, Georgia, serif;
}

.news-item__text {
    font-size: 15px;
    line-height: 1.5;
}

.news-item__text a, .interview-show__text a, .news-show__text a {
    color: #00BCBE;
}

.news-item__text a:hover, .interview-show__text a:hover {
    text-decoration: underline;
}

.interview-show img {
    width: 100%;
}

.interview-show .gallery-inline img {
    margin-bottom: 0px;
}

.interview-show__author {
    font-size: 15px;
    margin-bottom: 20px;
}

.interview-show__category {
    font-size: 16px;
    margin-bottom: 20px;
    color: #00BCBE;
}

.interview-show__title {
    font-size: 28px;
    margin-bottom: 8px;
    line-height: 1.2;
    font-family: "PF BeauSans W15 SemiBold", "Roboto", Cambria, Georgia, serif;
}

.interview-show__tagline {
    margin-bottom: 30px;
    font-size: 22px;
    font-family: 'PF BeauSans W15 Light Italic', "Roboto", Cambria, Georgia, serif;
}

.interview-show__text {
    font-size: 16px;
    line-height: 1.6;
    margin-top: 20px;
}

.interview-show__text p {
    margin: 0 0 5px 0;
}

.interview-show__text strong {
    color: #000;
}

.news-show__text {
    font-size: 16px;
    line-height: 1.6;
}

.external-link {
    margin-top: 10px;
}

.external-link a {
    color: #00BCBE;
}

.external-link a:hover {
    text-decoration: underline;
}

.view-gallery {
    font-family: "PF BeauSans W15 SemiBold", "Roboto", Cambria, Georgia, serif;
    text-align: right;
    font-size: 13px;
    margin-bottom: 20px;
}

.interview-item__date--homepage {
    font-size: 12px;
    color: #5d5d5d;
}

@media screen and (max-width: 1279px) {
    .interview-item__photo {
        width: 300px;
        height: 220px;
    }

    .interview-item__title {
        font-size: 17px;
        margin-bottom: 12px;
    }

    .interview-item__details {
        padding: 20px 20px;
    }

    .interview-item__details--homepage {
        padding: 0px 0px 20px;
    }
}

@media screen and (max-width: 767px) {
    .interview-show .gallery-inline img {
        height: 30vw;
    }

    .news-item__date {
        padding-left: 0px;
        padding-top: 10px;
    }

    .interview-item .table-cell {
        display: block;
    }

    .interview-item__photo {
        width: 100%;
        height: 230px;
    }

    .news-item .table-cell {
        display: block;
    }

    .news-item__cont {
        float: none;
        width: 100%;
        display: block;
    }

    .news-item__photo {
        width: 100%;
        height: 60vw;
        margin-bottom: 20px;
        display: block;
    }
}

/* ==========================================================================
   Pagination
========================================================================== */

.pagination {
    padding: 10px 0 0;
}

.pagination > li > a, .pagination > li > span {
    position: relative;
    float: left;
    width: 37px;
    height: 37px;
    padding: 0;
    border-radius: 50%;
    margin-left: 5px;
    line-height: 35px;
    font-size: 14px;
    text-align: center;
    color: #000;
    text-decoration: none;
    background-color: transparent;
    border: 0px solid #ddd;
}

.pagination > li > a:hover {
    color: #000
}

.pagination > .disabled > a, .pagination > .disabled > a:focus, .pagination > .disabled > a:hover, .pagination > .disabled > span, .pagination > .disabled > span:focus, .pagination > .disabled > span:hover {
    color: #777;
    cursor: not-allowed;
    background-color: transparent;
}

.pagination > .active > a, .pagination > .active > a:focus, .pagination > .active > a:hover, .pagination > .active > span, .pagination > .active > span:focus, .pagination > .active > span:hover {
    z-index: 2;
    color: #00BCBE;
    cursor: default;
    background-color: transparent;
    border: 1px solid #00BCBE;
}

/* ==========================================================================
    Search
   ========================================================================== */
.search-page {
    margin-top: 40px;
}

.search-form__input {
    font-size: 18px;
    border: 1px solid #eee;
    border-radius: 0;
    padding: 15px 0 15px 50px;
    height: auto;
    outline: 0;
    box-shadow: none !important;
}

.search-form__button {
    position: absolute;
    top: 13px;
    left: 14px;
}

.search-form__button button {
    font-size: 22px;
    color: #00BCBE;
    background-color: transparent;
    border: 0;
    margin: 0;
    padding: 0;
}

.search-page__nav {
    border: 0;
}

.search-page__nav--margin {
    margin-bottom: 30px;
}

.search-page__nav > li {
    margin: 5px 6px;
}

.search-page__nav > li:first-child {
    margin: 5px 6px 5px 0;
}

.search-page__nav > li:last-child {
    margin: 5px 0px 5px 6px;
}

.search-page__nav > li > a {
    background-color: transparent;
    border: 1px solid #ddd !important;
    border-radius: 15px;
    color: rgb(85, 85, 85);
    vertical-align: middle;
    line-height: 12px;
    font-size: 12px;
    font-weight: 400;
    text-align: center;
}

.search-page__nav > li.active > a,
.search-page__nav > li.active > a:focus,
.search-page__nav > li.active > a:hover {
    background-color: transparent;
    border: 1px solid #00BCBE !important;
    color: #00BCBE;
}

.search-page__nav > li > a:hover {
    background-color: transparent;
    border: 1px solid #00BCBE !important;
    color: #00BCBE;
}

.search-results .media-body {
    vertical-align: middle;
}

.search-results__title {
    font-size: 16px;
    margin-bottom: 15px;
    font-family: "PF BeauSans W15 SemiBold", "Roboto", Cambria, Georgia, serif;
}

.search-results-play {
    border-bottom: 1px solid #eee;
}

.search-results-play__photo {
    width: 100px;
    height: 75px;
    background-size: cover;
    background-position: center;
    background-color: #222;

}

.search-results-play__title {
    font-size: 14px;

}

.search-results__title--margin {
    margin-top: 40px;
}

.search-results-play__theater {
    color: #999;
    margin-top: 5px;
}

.search-results-play__dates {
    font-size: 12px;
    color: #00BCBE;
    margin-top: 4px;
}

.search-results-person {
    padding-bottom: 10px;
}

.search-results-person__name {
    font-size: 14px;
    vertical-align: middle;
}

.search-results-theater {
    padding-bottom: 10px;
}

.search-results-theater a {
    display: inline-block;
}

.search-results-theater__name {
    font-size: 14px;
    display: inline-block;
}

.search-results-theater__name:hover {
    text-decoration: underline;
}

.search-results-theater__info {
    margin-top: 2px;
    color: #999;
    font-size: 12px;
    display: inline-block;
}

.search-results-theater__info:hover {
}

.general-actions--search {
    padding-top: 14px;
    text-align: left;
    margin-bottom: 30px;
}

.search-results-play:hover .search-results-play__title,
.search-results-person:hover .search-results-person__name,
.search-results-theater:hover .search-results-theater__name {
}

@media screen and (max-width: 767px) {
    .search-results__title--mobileMargin {
        margin-top: 40px;
    }
}

/* ==========================================================================
    Theater
   ========================================================================== */
.theater-slider {
    background-color: #ccc;
}

.theater-header__name {
    font-size: 1.5rem;
}

.theater-header__address {
    font-size: 1rem;
    margin-top: 5px;
    margin-bottom: 10px;
    color: #777
}

.theater-info {
    background-color: #fff;
    margin: 20px 0;
    padding: 0px;
}

.theater-info__inner {
    padding: 30px;
}

.theater-info__name {
    font-size: 18px;
    margin-bottom: 10px;
}

.theater-info__address {
    font-size: 14px;
}

.theater-info__map {
    width: 100%;
    height: 250px;
}

.theater-carousel {
    width: 100%;
    height: 500px;
    background-size: cover;
}

.theater-carousel .table-cell {
    vertical-align: bottom;
}

.theater-carousel__info {
    background-color: rgba(0, 0, 0, 0.4);
    color: #fff;
    padding: 20px;
}

.theater-carousel__title {
    font-size: 1.7rem;
}

.theater-carousel__synopsis {
    font-size: 12px;
}

.theater-carousel__dates {
    font-size: 12px;
    margin-top: 6px;
}

.theater-archive__year {
    font-size: 24px;
    margin: 30px 0 5px;

}

.theater-archive-play {
    margin: 2px 0;
}

.theater-archive-play__image {
    width: 100px;
    min-height: 70px;
    background-size: cover;
    background-position: center;
    background-color: #9B9B9B;
}

.theater-archive-play__details {
    padding: 10px 20px;
}

.theater-archive-play__genre {
    font-size: 12px;
    color: #999;
    padding-top: 3px;
}

.theater-archive-play__details h3 {
    font-size: 16px;
}

.theater-socials {
    font-size: 12px;
    padding-right: 10px;
}

.theater-calendar {
    width: 100%;
    background-color: #fff;
    font-size: 14px;
    table-layout: fixed;
}

.theater-calendar .table-cell {
    text-align: center;
    vertical-align: middle;
}

.theater-calendar .timetable-date {
    padding: 20px 5px;
    text-align: center;
}

.owl-carousel-timetable {
}

.theater-findPlay--noResults {
    padding: 60px 0;
    font-size: 20px;
    color: #444;
}

.plays-loading {
    text-align: center;
    padding: 60px 0;
    font-size: 20px;
    color: #444;
}

.theater-calendar .timetable-date:hover {
    background-color: #f5f5f5;
    cursor: pointer;
}

.theater-calendar .theater-arrow {
    background-color: #EDEDED;
    font-size: 30px;
    padding: 0px 15px;
    width: 40px;
    line-height: 30px;
    vertical-align: middle;
}

.theater-calendar .theater-arrow:hover {
    background-color: #00BCBE;
    cursor: pointer;
    color: #fff;
}

@media screen and (max-width: 767px) {
    .theater-carousel {
        height: 50vw;
    }
}

@media screen and (max-width: 480px) {
    .theater-carousel {
        height: 50vw;
    }
}

/* ==========================================================================
    Watchlist
   ========================================================================== */
.watchlist-cont {
    padding-right: 30px;
}

.watchlist-button {
    cursor: pointer;
}

.watchlist-button .fa {
    color: #fff;
    text-align: center;
    border-radius: 3px;
    cursor: pointer;
    font-size: 20px;
    line-height: 1.6;
}

.play-rating-cont .watchlist-button .fa {
    opacity: 0.9
}

.play-rating-cont:hover .watchlist-button .fa {
    opacity: 1
}

@media screen and (max-width: 767px) {
    .search-results-c .watchlist-button .fa {
        color: #da4939;
        text-align: center;
        border-radius: 3px;
        cursor: pointer;
        font-size: 20px;
        line-height: 1.6;
    }
}

.watchlist-button--small {
    color: #da4939;
    border-radius: 0px;
    cursor: pointer;
    font-size: 20px !important;
    line-height: 20px;
    display: inline-block;
    position: absolute;
    right: 0px;
    top: 0px;
    padding: 3px 10px;
}

.watchlist-button--attached .fa {
    color: #da4939;
}

.watchlist-button--small.watchlist-button--attached {
    color: #da4939;
}

.watchlist-days {
    font-size: 12px;
    opacity: 0.8
}

.play-image-small.watchlist-days--alarm {
    border-left: 7px solid #d0a840;
}

.play-image-small.watchlist-days--ended {
    border-left: 7px solid #da4939;
}

.play-image-small.watchlist-days--starts {
    border-left: 7px solid #6997da;
}

.watchlist-days--alarm {
    color: #d0a840;
}

.watchlist-days--ended {
    color: #da4939;
}

.watchlist-days--starts {
    color: #6997da;
}

/* ==========================================================================
    Tickets button
   ========================================================================== */
.tickets-button {
    color: #333;
    cursor: pointer;
    font-size: 18px !important;
    display: inline-block;
    position: absolute;
    right: 0px;
    top: 0px;
    padding: 2px 3px;
    border-radius: 4px;
}

@media screen and (max-width: 767px) {
    .tickets-button__alt .tickets-button {
        right: 6px;
        top: 35px;
        font-size: 17px !important;
    }
}

.buy-ticket {
    color: #212529;
    cursor: pointer;
    font-size: 18px !important;
    line-height: 18px;
    display: inline-block;
    position: absolute;
    right: 4px;
    top: 14px;
    padding: 2px 3px;
    border-radius: 4px;
    background-color: #F9C21B;
}

.btn-tickets {
    color: #212529;
    padding: 9px 10px;
    background-color: #F9C21B;
    text-align: left !important;
}

.btn-tickets:hover, .btn-tickets:focus {
    color: #212529;
    padding: 9px 10px;
    background-color: #ffb000;
    text-decoration: none !important
}

/* ==========================================================================
    Follow button
   ========================================================================== */
.follow-button {
    color: #00BCBE;
    border: 1px solid #00BCBE;
    text-align: center;
    border-radius: 37px;
    cursor: pointer;
    font-size: 12px;
    display: inline-block;
    padding: 5px 14px
}

.follow-button--min {
    border-radius: 50%;
    font-size: 14px;
    padding: 0px;
    width: 30px;
    height: 30px;
    line-height: 30px;
}

.follow-button--icon {
    color: #fff;
    border: 1px solid #00BCBE;
    background-color: #00BCBE;
    text-align: center;
    border-radius: 5px;
    cursor: pointer;
    font-size: 12px;
    display: inline-block;
    padding: 6px 14px
}

.follow-button--icon.follow-button--attached {
    color: #00BCBE;
    border: 1px solid #00BCBE;
    background-color: transparent;
}

.follow-button--icon i {
    font-size: 15px;
    margin-right: 5px;
}

.follow-button--attached {
    background-color: #00BCBE;
    border: 1px solid #00BCBE;
    color: #fff;
}

/* ==========================================================================
    User Account
   ========================================================================== */
.alert {
    border-radius: 0px;
}

.alert-warning a {
    color: #8a6d3b;
}

#user-theater-list td {
    padding: 12px 0;
    border-bottom: 1px solid #e2e2e2;
}

#user-theater-list_filter label {
    width: 100%;
}

#user-theater-list_filter input {
    width: 100%;
    border: 0;
    background-color: #ededed;
    padding: 14px 14px 14px 45px;
    font-size: 18px;
    margin-bottom: 10px;
}

#user-theater-list_filter:before {
    content: '\f002';
    font-family: FontAwesome;
    font-style: normal;
    font-weight: normal;
    text-decoration: inherit;
    /*--adjust as necessary--*/
    color: #848484;
    font-size: 18px;
    padding-right: 0.5em;
    position: absolute;
    top: 14px;
    left: 16px;
}

#user-theater-list_filter input:focus {
    outline: 0;
    background-color: #ddd
}

.user-menu {
    padding: 60px 0 30px;
    font-size: 13px;
    text-align: center;
}

.user-menu a {
    margin: 5px 8px;
    display: inline-block;
    padding: 2px 0;
    letter-spacing: 0.12em;
    border-bottom: 2px solid transparent;
}

.user-menu a:hover, .user-menu a.active {
    border-bottom: 2px solid #00BCBE;
}

.user-list-item > .table-cell {
    padding: 12px 10px;
    border-bottom: 1px solid #e2e2e2;
    /*vertical-align: middle;*/
}

.user-list-item > .table-cell__noborder {
    padding: 0px 10px;
    border-bottom: 0px solid #e2e2e2;
}

.user-list-item .table-cell:first-child {
    padding-left: 0;
}

.user-list-item .table-cell:last-child {
    padding-right: 0;
}

.user-list-item i.pe-7s-close-circle {
    font-size: 30px;
    color: #bbb;
    cursor: pointer;
}

.user-list-item .item-title {
    font-size: 14px;
}

.user-list-item .item-descr {
    opacity: 0.6
}

.user-list-item .pe-7s-close-circle:hover {
    color: #da4939;
}

.user-list-item .pe-7s-plus:hover {
    color: #60a36f;
}

.user-list-item .pe-7s-check {
    color: #60a36f;
}

.user-list-item .links a {
    color: #00BCBE;
}

.user-list-item .links a:hover {
    text-decoration: underline;
}

.empty-state {
    padding: 60px 0;
    font-size: 20px;
}

.empty-state h4 {
    font-size: 18px;
    margin: 0;
    color: #333;
}

.empty-state_subtitle {
    font-size: 15px;
    margin-top: 8px;
    opacity: 0.9;
    color: #878787;
}

.empty-state i {
    font-size: 120px;
    color: #00BCBE;
}

.play-image-small {
    width: 100px;
    height: 70px;
    background-size: cover;
    background-position: center;
    background-color: #eee;
}

.play-container:hover .play-image-small {
    opacity: 0.9 !important;
}

.rating {
    width: 40px;
    height: 40px;
    text-align: center;
    display: inline-block;
    background-color: #f4f4f4;
    color: #00BCBE;
    border-radius: 50%;
    font-size: 22px;
    line-height: 40px;
    margin: 5px auto;
    white-space: nowrap
}

.rating--small {
    width: 38px;
    height: 28px;
    font-size: 14px;
    line-height: 28px;
    margin: 0;
}

.rating .fa {
    margin-right: 4px;
}

.play-rating {
    float: right;
}

.play-rating span {
    font-size: 12px;
    opacity: 0.8;
}

.ratings-all {
    margin-bottom: 40px;
}

.ratings-all a {
    display: inline-block;
    font-size: 13px;
    width: 35px;
    height: 35px;
    background-color: #eee;
    text-align: center;
    color: #aaa;
    border-radius: 50%;
    line-height: 35px;
    margin: 2px;
}

.ratings-all a:hover, .ratings-all a.active {
    background-color: #00BCBE;
    color: #fff;
}

.user-rating-cont .caption .label {
    font-size: 12px;
}

.auth-container {
    padding: 20px;
}

.login-socials {
    color: #fff;
    text-align: left;
    padding: 13px 15px 13px 55px;
    font-size: 14px;
    border-radius: 4px;
    margin: 5px 0px;
    position: relative;
    display: block;
    cursor: pointer;
}

.login-socials:hover {
    color: #fff;
    opacity: 0.9
}

.login-socials .fa {
    font-size: 20px;
    position: absolute;
    left: 20px;
    top: 13px;
}

.login-facebook {
    background-color: #3B5998;
}

.login-twitter {
    background-color: #1DA1F2;
}

.login-google {
    background-color: #D3392F;
}

.auth-link {
    padding-bottom: 5px;
    padding-top: 10px;
}

.auth-link a {
    color: #00BCBE;
}

.auth-link a:hover {
    text-decoration: underline;
}

.or {
    display: flex;
    justify-content: center;
    align-items: center;
    color: #a1a8af;
    padding: 20px 0;
}

.or:after,
.or:before {
    content: "";
    display: block;
    background: #a1a8af;
    width: 20%;
    height: 1px;
    margin: 0 10px;
}

/* ==========================================================================
    ratings
   ========================================================================== */
.rate-action {
    position: relative;
}

.rateIt {
    position: absolute;
    top: -3px;
    left: -298px;
    background-color: #1f1f1f;
    padding: 14px 15px;
    border-radius: 3px;
    width: 300px;
    text-align: center
}

#rateIt .play-rate__stars {
    display: inline-block;
}

#rateIt #clear-vote {
    display: inline-block;
    border-right: 1px solid rgba(255, 255, 255, 0.17);
    padding-right: 10px;
    margin-right: 10px;
    cursor: pointer;
    font-size: 16px;
}

.rate-mobile {
    display: none
}

.rate-mobile--img {
    max-width: 100%;
    max-height: 200px;
}

@media screen and (max-width: 767px) {
    .rate-mobile {
        display: block
    }

    .rateIt {
        position: fixed;
        top: 0;
        left: 0px;
        right: 0;
        bottom: 0;
        background-color: rgba(15, 15, 15, 0.95);
        padding: 70px 15px 70px;
        border-radius: 0px;
        width: 100%;
        z-index: 9999;
        overflow: scroll;
        -webkit-overflow-scrolling: touch;
    }
}

.rate-mobile h4 {
    margin-bottom: 20px;
}

.rate-mobile img {
    margin-bottom: 20px;
}

.play-rating-cont {
    color: rgba(255, 255, 255, 0.92);
    padding-top: 15px;
}

.play-rating-cont .table-cell {
    vertical-align: bottom !important
}

.rate-action:hover {
    cursor: pointer;
    color: rgba(255, 255, 255, 1);
}

.play-rating-cont .fa-star, .play-rating-cont .fa-star-o {
    font-size: 20px;
}

.play-icon {
    font-size: 19px;
    line-height: 1.6;
}

.play-icons-cont {
    padding: 0 10px;
}

.play-icons-cont .fa-pencil {
    font-size: 20px;
    margin-bottom: 6px;
}

.play-rating-cont .fa-star {
    color: #ffca00;
}

.play-rating--reviews {
    padding-bottom: 10px;
}

.play-rating--reviews .fa-star-o, .play-rating--reviews .fa-times {
    color: #999;
}

.play-rating--reviews .fa-star {
    color: #ffca00;
}

.play-rating--reviews #rateIt #clear-vote {
    border-right: 1px solid rgba(169, 169, 169, 0.52);
}

.play-rating-cont .rate-number {
    font-size: 20px;
    padding-left: 1px;
    display: inline-block;
}

.play-rating-cont .play-rating-users, .play-iconText {
    font-size: 11px;
    opacity: 0.7;
    white-space: nowrap
}

.rate-action:hover .play-rating-users,
.write-critic:hover .play-rating-users,
.play-icons-cont:hover .play-iconText {
    opacity: 1 !important
}


.play-rate__stars .fa {
    font-size: 18px;
    padding: 0 3px;
}

.play-rate__stars .fa {
    cursor: pointer;
}

@media screen and (max-width: 767px) {
    .play-rate__stars .fa {
        font-size: 20px;
        padding: 0 4px;
    }
}

@media screen and (max-width: 370px) {
    .play-rate__stars .fa {
        font-size: 18px;
        padding: 0 3px;
    }
}

.modal-dialog--rate {
    width: 300px !important;
}

.modal {
    text-align: center;
}

/*.modal:before {*/
/*display: inline-block;*/
/*vertical-align: middle;*/
/*content: " ";*/
/*height: 100%;*/
/*}*/

.modal-dialog {
    display: inline-block;
    text-align: left;
    vertical-align: middle;
}

.modal--full {
}

.modal-dialog--full {
    width: 100%;
    height: 100vh;
    margin: 0 !important;
    padding: 0;
    position: fixed;
    overflow-y: scroll;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
}

.modal-content--full {
    height: auto;
    min-height: 100%;
    border-radius: 0;
}

.modal-footer--sticky {
    position: fixed;
    bottom: 0;
    right: 0;
    left: 0;
    padding: 8px 15px 8px 0;
    background-color: #f9f9f9;
    z-index: 1051;
}

@media screen and (max-width: 1024px) {
    .modal-footer--sticky {
        padding: 8px 15px 8px 15px;
    }
}

/* ==========================================================================
    Other pages
   ========================================================================== */
.contact-sidebar {
    font-size: 16px;
}

.about_header {
    margin-bottom: 30px;
}

.terms-text h3 {
    font-size: 17px;
    font-family: "PF BeauSans W15 SemiBold", "Roboto", Cambria, Georgia, serif;
    margin: 35px 0 12px
}

.error-page {
    margin-top: 120px;
}

.error-page h1 {
    font-size: 20px;
    font-family: "PF BeauSans W15 SemiBold", "Roboto", Cambria, Georgia, serif;
    margin: 20px 0
}

.error-page h2 {
    font-size: 16px;
    line-height: 1.5;
    margin: 20px 0;
    opacity: 0.8
}

.error-page p {

}

.error-page a {
    color: #00BCBE;
}

.error-page a:hover {
    text-decoration: underline;
}

.close {
    font-size: 32px;
    top: 0px !important;
}

.dataTables_filter {
    position: relative;
}

#onBoardModal .tab-content {
    padding-bottom: 60px;
}

.play-grid__image--video {
    position: relative;
    cursor: pointer;
    margin-bottom: 5px;
}

.play-grid__image--video:hover .videoPlayButton {
    opacity: 1
}

.video-container {
    /*padding:0;*/
}

.videoPlayButton {
    position: absolute;
    top: calc(50% - 30px);
    left: calc(50% - 30px);
    opacity: 0.7;
}

.modal-video .modal-header {
    border-bottom: 0px solid #e5e5e5;
}

.modal-video .modal-content {
    background-color: transparent;
    border: 0px solid rgb(255, 255, 255);
}

.modal-video .modal-body {
    padding: 0;
}

.modal-video .close {
    float: right;
    font-size: 28px;
    font-weight: 700;
    line-height: 1;
    color: #fff;
    text-shadow: 0 1px 0 transparent;
    filter: alpha(opacity=50);
    opacity: .5;
}

.modal-video .modal-header {
    padding: 0;
}

.modal-video {
    text-align: center;
    padding: 0 !important;
}

.modal-video:before {
    content: '';
    display: inline-block;
    height: 100%;
    vertical-align: middle;
    margin-right: -4px;
}

.modal-video .modal-dialog {
    display: inline-block;
    text-align: left;
    vertical-align: middle;
}

@media screen and (max-width: 767px) {
    .play-grid__details {
        margin-bottom: 20px;
    }
}

.trailers-header {
    margin-top: 40px;
}

.promoteBanner {
    background-color: #E3F8F8;
    border-top: 1px solid #abe9ea;
    border-bottom: 1px solid #abe9ea;
    position: relative
}

.promoteBanner .table-cell {
    vertical-align: middle;
}

.promoteBanner .table-cell + .table-cell {
    vertical-align: bottom;
}

.promoteBanner__cont {
    padding: 30px;
}

.promoteBanner__title {
    font-weight: 600;
    font-size: 18px;
    line-height: 1.3;
    margin-bottom: 7px;
}

.promoteBanner__text {
    font-weight: 300;
    font-size: 15px;

}

.promoteBanner__photo {
    width: 200px;
}

.promoteBanner__button {
    margin-top: 20px;
    font-size: 14px;
    display: inline-block;
    background-color: #00BCBE;
    color: #fff;
    padding: 10px 20px;
    border-radius: 4px;
}

.promoteBanner__button:hover {
    background-color: #00BCBE;
    color: #fff;
}

.promoteClose {
    position: absolute;
    top: 2px;
    right: 10px;
    color: #4cb4b5;
    cursor: pointer
}

.promoteClose:hover {
    color: #409899;
}

@media screen and (max-width: 767px) {
    .promoteBanner__photo {
        width: 170px;
    }
}

@media screen and (max-width: 500px) {
    .promoteBanner .table-cell {
        display: block;
    }

    .promoteBanner .table-cell + .table-cell {
        vertical-align: bottom;
        text-align: right;
    }

    .promoteBanner__photo {
        margin-top: -90px;
        width: 150px;
    }
}

/* Promote stream banner */
.promoteStreamBanner {
    background-color: rgba(224, 43, 75, 0.1);
    border-top: 1px solid rgba(224, 43, 75, 0.2);
    border-bottom: 1px solid rgba(224, 43, 75, 0.2);
    position: relative
}

.promoteStreamBanner .table-cell {
    vertical-align: middle;
}

/*.promoteStreamBanner .table-cell + .table-cell {*/
/*    vertical-align: middle;*/
/*}*/
.promoteStreamBanner__imgCont {
    padding: 20px 20px 20px 30px;
    width: 160px;
}

.promoteStreamBanner__cont {
    padding: 20px 30px 20px 30px;
}

.promoteStreamBanner__title {
    font-weight: 600;
    font-size: 18px;
    line-height: 1.3;
    margin-bottom: 7px;
}

.promoteStreamBanner__text {
    font-weight: 300;
    font-size: 15px;
    padding: 20px 20px;
}

.promoteStreamBanner__photo {
    width: 160px;
}

.promoteStreamBanner__button {
    font-size: 14px;
    display: inline-block;
    background-color: rgba(224, 43, 75, 0.8);
    color: #fff;
    padding: 10px 20px;
    border-radius: 4px;
    white-space: nowrap;
}

.promoteStreamBanner__button:hover {
    background-color: rgba(224, 43, 75, 1);
    color: #fff;
}


.promoteStreamClose:hover {
    color: rgba(224, 43, 75, 0.8);
}

@media screen and (max-width: 767px) {
    .promoteStreamBanner .table-cell {
        display: block;
    }

    .promoteStreamBanner__imgCont {
        padding: 20px 20px 0px 20px;
        width: 160px;
    }

    .promoteBanner__photo {
        width: 170px;
    }

    .promoteStreamBanner__cont {
        padding: 0px 30px 20px 20px;
    }

    .promoteStreamBanner__text {
        padding: 10px 20px;
    }
}

@media screen and (max-width: 500px) {
    .promoteStreamBanner .table-cell {
        display: block;
    }

    .promoteStreamBanner .table-cell + .table-cell {
        vertical-align: middle;
    }

    .promoteStreamBanner__photo {
        width: 150px;
    }
}


.userCritic__rating {
    width: 40px;
    white-space: nowrap;
    font-size: 16px;
    color: #6f6f6f;
    margin-left: 5px;
}

.userCritic__rating .fa {
    color: #ffca00;
    font-size: 15px;
}

.userCritic__rating--total {
    font-size: 10px;
}

.write-critic {
    cursor: pointer;
    display: inline-block;
    color: rgba(255, 255, 255, 0.92);
}

.play-rating-cont .write-critic:hover, .play-rating-cont .write-critic:focus {
    color: rgba(255, 255, 255, 1);
}

.spoiler-switch {
    padding-top: 10px;
}

.spoiler-switch .onoffswitch {
    width: 35px;
}

.spoiler-switch .onoffswitch-label {
    height: 19px;
}

.spoiler-switch .onoffswitch-label:before {
    width: 19px;
}

.spoiler-switch__label {
    margin-left: 10px;
    opacity: 0.7
}

.user-reviews__item {
    padding: 10px 0 20px 0;
    margin-bottom: 20px;
    border-bottom: 1px solid #e2e2e2;
}

.user-comments__title {
    margin-bottom: 7px;
}

.upvoteReview .fa {
    color: #999;
    font-size: 17px;
}

.upvoteReview .fa:hover {
    cursor: pointer
}

.upvoteReview .fa.fa-thumbs-up {
    color: #bbbbbb;
}

.user-comment .reviewCount {
    color: #aaa;
    font-size: 12px;
}

.deleteReview {
    padding: 20px;
    text-align: center;
    border-top: 1px solid #e4e4e4;
    background-color: #fff;
}

.deleteReview-button {
    cursor: pointer;
    font-size: 13px;
    line-height: 10px;
    padding: 0;
    background-color: transparent;
    border: 0;
    outline: 0;
}

.deleteReview-button i {
    font-size: 16px;
    height: 20px;
    vertical-align: middle;
}

.deleteReview-button span {
}

.deleteReview-button:hover {
    color: #ff5508;
}

.spoiler-tag {
    font-size: 12px;
    color: #ff5508;
}

.addReviewButtonCont {
    margin-bottom: 10px;
}

#toast-container > .toast-success {
    position: relative;
    pointer-events: auto;
    overflow: hidden;
    margin: 0 0 6px;
    padding: 12px 15px 10px 15px;
    width: 300px;
    -moz-border-radius: 3px;
    -webkit-border-radius: 3px;
    border-radius: 3px;
    background-position: 15px center;
    background-repeat: no-repeat;
    -moz-box-shadow: 0 0 6px rgba(0, 0, 0, 0.2);
    -webkit-box-shadow: 0 0 6px rgba(0, 0, 0, 0.2);
    box-shadow: 0 0 6px rgba(0, 0, 0, 0.2);
    background-color: #368082;
    color: #FFF;
    opacity: 1;
    -ms-filter: progid:DXImageTransform.Microsoft.Alpha(Opacity=100);
    filter: alpha(opacity=100);
}

#toast-container > .toast-success {
    background-image: none !important
}

.toast-close-button {
    position: relative;
    right: -4px !important;
    top: -2px !important;
    float: right;
    font-size: 20px;
    font-weight: 700;
    color: #FFF;
    -webkit-text-shadow: 0 1px 0 #fff;
    text-shadow: 0 1px 0 #fff;
    opacity: .8;
    -ms-filter: progid:DXImageTransform.Microsoft.Alpha(Opacity=80);
    filter: alpha(opacity=80);
    line-height: 1;
    outline: none;
}

.toast-close-button:focus, .toast-close-button:hover {
    color: #FFF;
    text-decoration: none;
    cursor: pointer;
    opacity: 1;
    -ms-filter: progid:DXImageTransform.Microsoft.Alpha(Opacity=100);
    filter: alpha(opacity=100);
}

#toast-container > div:hover {
    -moz-box-shadow: 0 0 6px rgba(0, 0, 0, 0.2);
    -webkit-box-shadow: 0 0 6px rgba(0, 0, 0, 0.2);
    box-shadow: 0 0 6px rgba(0, 0, 0, 0.2);
    opacity: 1;
    -ms-filter: progid:DXImageTransform.Microsoft.Alpha(Opacity=100);
    filter: alpha(opacity=100);
    cursor: pointer;
}

.toast-top-center {
    top: 5px;
    right: 0;
    width: 100%;
}

.toast-message {
    font-size: 13px;
}

.videoInfo {
    font-size: 11px;
    color: rgb(96, 96, 96);
}

.accordion {
    position: relative;
    background: linear-gradient(to bottom right, white, #F8F8F8);
    background: white;
    margin: 0 auto;
}

.accordion-header {
    position: relative;
    border-bottom: 1px solid #e9e9e9;
    color: #2a313a;
    cursor: pointer;
    font-weight: 700;
    padding: 1.5rem 0rem;
}

.accordion-header::after {
    font-family: FontAwesome;
    content: "\f107";
    position: absolute;
    color: #000;
    font-size: 22px;
    right: 20px;
    top: 10px;
}

.active.accordion-header::after {
    content: "\f106";
}

.accordion-content {
    display: none;
    border-bottom: 1px solid #DDE0E7;
    /*background: #f8f8f8;*/
    padding: 1.5rem 2rem;
}

.accordion-content.open {
    display: block
}

@media (max-width: 991px) {
    .accordion-header {
        padding: 1.5rem 2.5rem;
    }

    .accordion-content {
        padding: 1.5rem 2.5rem;
    }
}

.personNews__details {
    padding: 15px 0;
}

.personNews__title {
    font-size: 14px;
    font-family: "PF BeauSans W15 SemiBold", "Roboto", Cambria, Georgia, serif;
}

.personNews__photo {
    width: 100%;
    height: 220px;
    background-size: cover;
    background-position: center;
}

@media (max-width: 767px) {
    .personNews__photo {
        height: 30vw;
    }

    .play-grid__image--videoResponsive {
        height: 30vw;
    }
}

@media (max-width: 480px) {
    .personNews__photo {
        height: 50vw;
    }

    .play-grid__image--videoResponsive {
        height: 50vw;
    }
}

.sidebarBanner + .sidebarBanner {
    margin-top: 5px;
}

.play-critique {
    /*padding-top:30px;*/
    /*padding-bottom:30px;*/
}

.play-critique__title {
    font-size: 13px;
    font-family: "PF BeauSans W15 SemiBold", "Roboto", Cambria, Georgia, serif;
    margin-bottom: 7px;
}

.play-critique__text {
    margin-bottom: 12px;
    font-size: 16px;
}

.proButton {
    display: inline-block;
    margin-top: 20px;
    padding: 10px 30px;
    background-color: #FFAF00;
    font-family: "PF BeauSans W15 SemiBold", "Roboto", Cambria, Georgia, serif;
    color: #222;
    border-radius: 5px;
    border: 0;
    font-size: 14px;
}

.proButton:hover, .proButton--small:hover {
    background-color: #ffa806;
    color: #222;
}

.collaborationsForm {
    background-color: #fff;
    padding: 30px;
}

.collaborations__title {
    font-size: 20px;
    margin-bottom: 10px;
    font-family: "PF BeauSans W15 SemiBold", "Roboto", Cambria, Georgia, serif;
}

.collaborationsForm__title {
    font-size: 20px;
    font-family: "PF BeauSans W15 SemiBold", "Roboto", Cambria, Georgia, serif;
    margin-bottom: 20px;
}

.collaborations__statNumber {
    font-size: 30px;
    font-family: "PF BeauSans W15 SemiBold", "Roboto", Cambria, Georgia, serif;
    margin-bottom: 0px;
    line-height: 1.1
}

.collaborations__stat {
    margin-bottom: 20px;
}

.collaborations__statText {
    font-size: 16px;
}

.collaborationsBullets {
    margin-left: -20px;
    margin-bottom: 20px;
}

.collaborationsBullets li {
    padding-bottom: 5px;
    font-size: 15px;
}

.collaborationsSection {
    background-color: #fff;
    padding: 30px;
    margin-bottom:5px;
}
@media screen and (max-width: 767px) {

    .collaborationsSection {
        padding: 20px 15px;
    }
    .collaborationsForm {
        padding: 20px 15px;
    }
    .collaborations__statNumber {
        font-size: 26px;
    }
}

.sidebarBannerPro {
    background-color: #034e70;
    padding: 30px 25px;
}

.sidebarBannerPro img {
    width: 70%;
    max-width: 150px;
}

.sidebarBannerPro__text {
    color: #fff;
    font-size: 16px;
    margin-top: 20px;
    margin-bottom: 20px;
}

.sidebarBannerPro__button {
    background-color: #fff;
    padding: 5px 25px;
    color: #034e70;
    border-radius: 5px;
    display: inline-block;
    font-family: "PF BeauSans W15 SemiBold", "Roboto", Cambria, Georgia, serif;
}

.sidebarBannerPro:hover .sidebarBannerPro__button {
    background-color: rgba(255, 255, 255, 0.91);
    color: #034e70;
}

.dropdown-menu > li > a:focus, .dropdown-menu > li > a:hover {
    background-color: rgba(0, 188, 190, 0.14)
}

.dropdownSocial {
    background-color: rgba(0, 0, 0, 0.30);
    border-radius: 4px;
    padding: 5px 10px;
    color: #ccc;
    cursor: pointer
}

.dropdownSocial:hover {
    background-color: rgba(0, 0, 0, 0.47);
}

.dropdownSocial i {
    opacity: 0.7
}

.sharePage {
    display: inline-block;
    margin-left: 5px;
}

.dropdown-menu--share {
    width: auto;
    min-width: auto;
    color: #ccc;
    background-color: #171717;
}

.dropdown-menu--share i {
    color: #fff;
    width: 20px;
}

.dropdown-menu.dropdown-menu--share > li > a {
    padding: 3px 20px;
    color: #b7b7b7;
    font-size: 13px;
}

.dropdown-menu.dropdown-menu--share > li > a:focus, .dropdown-menu.dropdown-menu--share > li > a:hover {
    background-color: rgb(0 0 0 / 93%);
}

.mainAvatar {
    width: 35px;
    height: 35px;
    border-radius: 50%
}

.sidebarBannerCont {
    margin-bottom: 10px;
}

.schools__name {
    font-size: 30px;
    line-height: 38px;
    font-family: "PF BeauSans W15 SemiBold", "Roboto", Cambria, Georgia, serif;
}

.schools__area {
    font-size: 18px;
    line-height: 28px;
    font-family: "PF BeauSans W15 SemiBold", "Roboto", Cambria, Georgia, serif;
}

.school__social {
    font-size: 26px;
}

.schools__description {
    margin: 20px 0 40px;
}

.school__photo {
    width: 100%;
    padding-top: 120%;
    position: relative;
    overflow: hidden;
    height: auto !important;
    box-shadow: 0px 0px 8px rgba(0, 0, 0, 0.05);
}
.school__photo--empty{
    background-color:#f1f1f1;
}
.school__photo img {
    object-fit: cover;
    object-position: center;
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    transition: transform .5s ease;
}

.schoolAlumnus__name {
    margin-top: 7px;
    margin-bottom: 25px;
    line-height:1.2em
}
.row.equal {
    display: flex;
    display: -webkit-flex;
    flex-wrap: wrap;
}
@media (min-width: 768px) {
    .text-md-right {
        text-align: right;
    }
}

a:hover .ads_teaser{
    background-color: rgba(255, 100, 9, 0.08);
}

.ads_teaser{
    width:100%;
    border-radius:5px;
    text-align: center;
    padding:25px 10px 15px;
    border:1px solid rgba(255, 91, 0, 0.35);
    background-color: rgba(255, 100, 9, 0.05);
    font-family: "PF BeauSans W15 Bold", "Roboto", Cambria, Georgia, serif;
}
.ads_teaser_title{
    font-size:18px;
    color: #E36423;
    line-height:24px;
}
.ads_teaser_text{
    font-size:14px;
    margin-top:30px;
    line-height:18px;
    font-family: "PF BeauSans W15 Regular", "Roboto", Cambria, Georgia, serif;
}
.ads_teaser_text span{
    display:block;
    font-family: "PF BeauSans W15 Bold", "Roboto", Cambria, Georgia, serif;
}

.ads_teaser img{
    width:50%;
    margin-bottom:20px;
}

a:hover .ads_teaser_main{
    background-color: rgba(255, 100, 9, 0.08);
}

.ads_teaser_main{
    width:100%;
    align-items: center;
    border-radius:5px;
    justify-content: center;
    display:flex;
    gap: 20px;
    padding:30px 15px ;
    border:1px solid rgba(255, 91, 0, 0.35);
    background-color: rgba(255, 100, 9, 0.05);
    font-family: "PF BeauSans W15 Bold", "Roboto", Cambria, Georgia, serif;
}
.ads_teaser_main_title{
    font-size:18px;
    color: #E36423;
    line-height:24px;
}
.ads_teaser_main_text{
    font-size:14px;
    line-height:18px;
    margin-top:10px;
    font-family: "PF BeauSans W15 Regular", "Roboto", Cambria, Georgia, serif;
}
.ads_teaser_main_text span{
    font-family: "PF BeauSans W15 Bold", "Roboto", Cambria, Georgia, serif;
}

.ads_teaser_main img{
    width:120px;
}

/* ==========================================================================
    Language Switcher
   ========================================================================== */

.language-switcher .dropdown-toggle {
    padding: 8px 12px;
    border-radius: 4px;
    transition: background-color 0.2s ease;
}

.language-switcher .dropdown-toggle:hover {
    background-color: rgba(0, 0, 0, 0.05);
}

.language-switcher .fa-globe {
    margin-right: 5px;
    color: #666;
}

.language-switcher .dropdown-menu {
    min-width: 120px;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.language-switcher .dropdown-menu li a {
    padding: 8px 15px;
    font-size: 14px;
    transition: background-color 0.2s ease;
}

.language-switcher .dropdown-menu li a:hover {
    background-color: #f5f5f5;
}

.language-switcher .dropdown-menu li a .fa-globe {
    margin-right: 8px;
    color: #999;
}

@media (max-width: 767px) {
    .language-switcher {
        display: none;
    }
}
