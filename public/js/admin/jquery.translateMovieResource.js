$(document).ready(function() {

    // Handle translation suggestion for title
    $('#translate_title').click(function(e) {
        e.preventDefault();

        var url = $(this).data('url');
        var movieId = $('input[name="movie_id"]').val() || window.location.pathname.split('/').slice(-2, -1)[0];

        // Show loading state
        $(this).html('<i class="fa fa-spinner fa-spin"></i> Μετάφραση...');
        $(this).prop('disabled', true);

        $.ajax({
            url: url,
            type: 'POST',
            data: {
                movie_id: movieId,
                field: 'title',
                _token: $('input[name="_token"]').val()
            },
            success: function(response) {
                if (response.success) {
                    $('#title_en').val(response.translation);
                    $('#translate_title').html('Πρότεινε μετάφρασιν');
                    $('#translate_title').prop('disabled', false);

                    // Show success message
                    if ($('.alert-success').length === 0) {
                        $('#title_en').after('<div class="alert alert-success alert-dismissible mt-2" role="alert">' +
                            '<button type="button" class="close" data-dismiss="alert" aria-label="Close">' +
                            '<span aria-hidden="true">&times;</span></button>' +
                            '<i class="icon md-check" aria-hidden="true"></i> Η μετάφραση προτάθηκε επιτυχώς!' +
                            '</div>');
                    }
                } else {
                    alert('Σφάλμα μετάφρασης: ' + response.message);
                    $('#translate_title').html('Πρότεινε μετάφρασιν');
                    $('#translate_title').prop('disabled', false);
                }
            },
            error: function() {
                alert('Σφάλμα σύνδεσης με την υπηρεσία μετάφρασης');
                $('#translate_title').html('Πρότεινε μετάφρασιν');
                $('#translate_title').prop('disabled', false);
            }
        });
    });

    // Handle translation suggestion for synopsis
    $('#translate_synopsis').click(function(e) {
        e.preventDefault();

        var url = $(this).data('url');
        var movieId = $('input[name="movie_id"]').val() || window.location.pathname.split('/').slice(-2, -1)[0];

        // Show loading state
        $(this).html('<i class="fa fa-spinner fa-spin"></i> Μετάφραση...');
        $(this).prop('disabled', true);

        $.ajax({
            url: url,
            type: 'POST',
            data: {
                movie_id: movieId,
                field: 'synopsis',
                _token: $('input[name="_token"]').val()
            },
            success: function(response) {
                if (response.success) {
                    // For WYSIWYG editors, we need to set content differently
                    if (typeof tinymce !== 'undefined' && tinymce.get('synopsis_en')) {
                        tinymce.get('synopsis_en').setContent(response.translation);
                    } else {
                        $('#synopsis_en').val(response.translation);
                    }

                    $('#translate_synopsis').html('Πρότεινε μετάφρασιν');
                    $('#translate_synopsis').prop('disabled', false);

                    // Show success message
                    if ($('.alert-success').length === 0) {
                        $('#synopsis_en').after('<div class="alert alert-success alert-dismissible mt-2" role="alert">' +
                            '<button type="button" class="close" data-dismiss="alert" aria-label="Close">' +
                            '<span aria-hidden="true">&times;</span></button>' +
                            '<i class="icon md-check" aria-hidden="true"></i> Η μετάφραση προτάθηκε επιτυχώς!' +
                            '</div>');
                    }
                } else {
                    alert('Σφάλμα μετάφρασης: ' + response.message);
                    $('#translate_synopsis').html('Πρότεινε μετάφρασιν');
                    $('#translate_synopsis').prop('disabled', false);
                }
            },
            error: function() {
                alert('Σφάλμα σύνδεσης με την υπηρεσία μετάφρασης');
                $('#translate_synopsis').html('Πρότεινε μετάφρασιν');
                $('#translate_synopsis').prop('disabled', false);
            }
        });
    });

});
