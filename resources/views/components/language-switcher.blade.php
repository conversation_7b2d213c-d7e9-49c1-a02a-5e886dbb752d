@php
    $currentLocale = app()->getLocale();
    $allowedLocales = config('locale.allowed_locales');
    $localeNames = __('locale.locales');
@endphp

<li class="dropdown language-switcher">
    <a href="#" class="dropdown-toggle" data-toggle="dropdown" role="button" aria-haspopup="true" aria-expanded="false">
        <div class="menu-items menu-items-link--text">
            <i class="fa fa-globe"></i>
            <span class="hidden-xs">{{ $localeNames[$currentLocale] ?? strtoupper($currentLocale) }}</span>
            <span class="visible-xs">{{ strtoupper($currentLocale) }}</span>
        </div>
    </a>
    <ul class="dropdown-menu">
        @foreach($allowedLocales as $locale)
            @if($locale !== $currentLocale)
                <li>
                    <a href="{{ switchLanguageUrl($locale) }}" 
                       data-trtype="language_switch_{{ $locale }}" 
                       class="tr_link">
                        {{ $localeNames[$locale] ?? strtoupper($locale) }}
                    </a>
                </li>
            @endif
        @endforeach
    </ul>
</li>
