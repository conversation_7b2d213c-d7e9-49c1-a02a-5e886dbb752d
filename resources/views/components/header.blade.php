<div id="header" class="header">
    <div class="table menu animated  ">
        <div class="table-cell table-cell--middle" style="width:1%;">
            <a data-trtype="header_logo" class="header__logo tr_logo" href="{{ route('homepage') }}">
                @php $logoVersion = app('router')->is('homepage' ) ? '_white':'' @endphp
                <img src="{{unstageAsset('web-icons/unstage_new_logo'. $logoVersion.'.svg')}}"/>
            </a>
        </div>
        <div class="table-cell table-cell--middle   ">
            <div class="header-search">
                <form method="get" name="header-search-form" id="header-search-form"
                      action="{{ route('search') }}">
                    <div class="table">
                        <div class="table-cell table-cell-full table-cell--middle">
                            <div type="submit" class="  header-search-button">
                                <i class="fa fa-search"></i>
                            </div>
                            <input id="autocomplete" name="q" autocomplete="off"
                                   placeholder="Αναζήτησε παραστάσεις, ηθοποιούς, θέατρα" value="">
                        </div>
                    </div>
                </form>
            </div>
        </div>
        <div class="table-cell   table-cell--middle text-right header-right ">
            <ul class="nav navbar-nav navbar-right  ">
                <li class="dropdown">
                    <a href="#" class="dropdown-toggle" data-toggle="dropdown" role="button" aria-haspopup="true"
                       aria-expanded="false">
                        <div class="menu-items menu-items-link--text hidden-sm hidden-xs">Ανακάλυψε</div>
                        <div class="visible-sm visible-xs">
                            <button class="hamburger  @if(!empty(auth('users')->user()))  loggedIn @endif  "
                                    type="button">
              <span class="hamburger-box">
                <span class="hamburger-inner"></span>
              </span>
                            </button>
                        </div>
                    </a>
                    <ul class="dropdown-menu">
                        @if(!empty(auth('users')->user()))
                            <li class="visible-sm visible-xs">
                                <a data-trtype="main_menu_my_settings" class="tr_link" href="{{route('neptune.profile.edit')}}">Προφίλ</a>
                            </li>
                            <li class="visible-sm visible-xs">
                                <a data-trtype="main_menu_my_watchlist" class="tr_link" href="{{route('neptune.watchlists.show')}}">
                                    Watchlist
                                </a>
                            </li>
                            <li class="visible-sm visible-xs">
                                <a data-trtype="main_menu_my_ratings" class="tr_link" href="{{route('neptune.ratings.index')}}">
                                    Βαθμολογίες
                                </a>
                            </li>
                            <li class="visible-sm visible-xs">
                                <a data-trtype="main_menu_my_reviews" class="tr_link" href="{{route('neptune.reviews.index')}}">
                                    Κριτικές
                                </a>
                            </li>
                            <li class="visible-sm visible-xs">
                                <a data-trtype="main_menu_my_people" class="tr_link" href="{{route('neptune.followables.people.index')}}">
                                    Συντελεστές μου
                                </a>
                            </li>
                            <li class="visible-sm visible-xs">
                                <a data-trtype="main_menu_my_theatres" class="tr_link" href="{{route('neptune.followables.theatres.index')}}">
                                    Θέατρα
                                </a>
                            </li>
                            <li class="visible-sm visible-xs">
                                <a data-trtype="main_menu_my_genres" class="tr_link" href="{{route('neptune.followables.supergenres.index')}}">
                                    Κατηγορίες
                                </a>
                            </li>
                            @if(auth('users')->user()->isPro())
                                <li class="visible-sm visible-xs"><a data-trtype="main_menu_my_pro" class="tr_link" target="_blank" href="{{route('neptune.pro.home')}}">unstagePro</a></li>
                            @endif
                            <li class="visible-sm visible-xs"><a data-trtype="main_menu_logout" class="tr_link" href="{{route('logout')}}">Έξοδος</a></li>
                        @else
                            <li class="visible-sm visible-xs"><a data-trtype="main_menu_register" class="tr_link" href="{{route('register.show')}}">Εγγραφή</a></li>
                            <li class="visible-sm visible-xs"><a data-trtype="main_menu_login" class="tr_link" href="{{route('login.show')}}">Είσοδος</a></li>
                        @endif
                        <li role="separator" class="divider visible-sm visible-xs"></li>
{{--                        <li><a data-trtype="main_menu_plays" class="tr_link" href="{{route('onDemand.home')}}">Online παραστάσεις</a></li>--}}
                        <li><a data-trtype="main_menu_plays" class="tr_link" href="{{route('plays.index')}}">Παραστάσεις</a></li>
                        <li><a data-trtype="main_menu_trailers" class="tr_link" href="{{route('videos.index')}}">Videos</a></li>
                        <li><a data-trtype="main_menu_interviews" class="tr_link" href="{{ route('interviews.index') }}">Συνεντεύξεις</a></li>
                        <li><a data-trtype="main_menu_news" class="tr_link" href="{{ route('news.index') }}">Νέα, εξελίξεις</a></li>
                        <li><a data-trtype="main_menu_people" class="tr_link" href="{{ route('people.index') }}">Συντελεστές</a></li>
                        <li><a data-trtype="main_menu_festivals" class="tr_link" href="{{ route('festivals.index') }}">Φεστιβάλ</a></li>
                        <li><a data-trtype="main_menu_about" class="tr_link" href="{{ route('about.index') }}">Σχετικά με εμάς</a></li>
                        <li><a data-trtype="main_menu_contact" class="tr_link" href="{{ route('contact.show') }}">{!! __('navigation.header.contact') !!}</a></li>
                        <li><a data-trtype="main_menu_pro" class="tr_link" href="{{route('neptune.pro.landing')}}">{!! __('navigation.header.pro') !!}</a></li>
                        {{-- <li role="separator" class="divider visible-sm visible-xs"></li>
                        @foreach(config('locale.allowed_locales') as $locale)
                            @if($locale !== app()->getLocale())
                                <li class="visible-sm visible-xs">
                                    <a href="{{ switchLanguageUrl($locale) }}"
                                       data-trtype="mobile_language_switch_{{ $locale }}"
                                       class="tr_link">
                                        <i class="fa fa-globe"></i> {{ __('locale.locales')[$locale] ?? strtoupper($locale) }}
                                    </a>
                                </li>
                            @endif
                        @endforeach --}}
                    </ul>
                </li>
                {{-- @include('components.language-switcher') --}}
                <li class="dropdown hidden-xs  hidden-sm">
                    @if(!empty(auth('users')->user()))
                        <a href="#" class="dropdown-toggle" data-toggle="dropdown" role="button" aria-haspopup="true"
                           aria-expanded="false">
                            <div class="menu-items menu-items-link ">
                                <img src="{{auth('users')->user()->avatar}}" class="mainAvatar"/>
                            </div>
                        </a>
                        <ul class="dropdown-menu">
                            @if(!empty(auth('users')->user()))
                                <li>
                                    <a data-trtype="main_menu_my_settings" class="tr_link" href="{{route('neptune.profile.edit')}}">
                                        Προφίλ
                                    </a>
                                </li>
                                <li>
                                    <a data-trtype="main_menu_my_watchlist" class="tr_link" href="{{route('neptune.watchlists.show')}}">
                                        Watchlist
                                    </a>
                                </li>
                                <li>
                                    <a data-trtype="main_menu_my_ratings" class="tr_link" href="{{route('neptune.ratings.index')}}">
                                        Βαθμολογίες
                                    </a>
                                </li>
                                <li>
                                    <a data-trtype="main_menu_my_reviews" class="tr_link" href="{{route('neptune.reviews.index')}}">
                                        Κριτικές
                                    </a>
                                </li>
                                <li>
                                    <a data-trtype="main_menu_my_people" class="tr_link" href="{{route('neptune.followables.people.index')}}">
                                        Συντελεστές
                                    </a>
                                </li>
                                <li>
                                    <a data-trtype="main_menu_my_theatres" class="tr_link" href="{{route('neptune.followables.theatres.index')}}">
                                        Θέατρα
                                    </a>
                                </li>
                                <li>
                                    <a data-trtype="main_menu_my_genres" class="tr_link" href="{{route('neptune.followables.supergenres.index')}}">
                                        Κατηγορίες
                                    </a>
                                </li>
                                @if(auth('users')->user()->isPro())
                                    <li><a data-trtype="main_menu_my_pro" class="tr_link" target="_blank" href="{{route('neptune.pro.home')}}">unstagePro</a></li>
                                @endif
                                <li role="separator" class="divider"></li>
                                <li><a data-trtype="main_menu_logout" class="tr_link" href="{{route('logout')}}">Έξοδος</a></li>
                            @endif
                        </ul>
                    @else
                        <div class="menu-items   ">
                            <a data-trtype="main_menu_login" href="{{route('login.show')}}" class="tr_link btn btn-login">Είσοδος</a>
                        </div>
                    @endif
                </li>
            </ul>
        </div>
    </div>
</div>
