<footer class="footer  ">
    <div class="">
        <div class="container-fluid  ">
            @if($recentlyViewedResources = session()->get('recentlyViewedResources'))
                <div class="row   ">
                    <div class="col-md-12 text-center">
                        <?php $count = 0 ?>
                        @foreach(array_reverse(array_slice($recentlyViewedResources,-Config::get('limit.recentlyViewedResources'),Config::get('limit.recentlyViewedResources'))) as  $resource)
                            <?php $count += 1 ?>
                            <a href="{{ route($resource['route'], ['slug' => $resource['slug'], 'ref_' => \App\Components\UrlReferrals::getReferral('play_details', 'generic_recently_viewed')]) }}"   class="recently_viewed_item @if($count > 13) history-lg @endif @if($count > 11) history-md @endif @if($count > 8) history-sm @endif @if($count > 6) history-xs @endif">
                                <div class="history-item b-lazy" data-src="{{ $resource['imageURL']?asset(Croppa::url($resource['imageURL'],200,null)): (array_key_exists('alternativeImageUrl',$resource)?$resource['alternativeImageUrl']:null)}}" data-toggle="tooltip" data-placement="top" title="{{ $resource['title'] }}"
                                     style="  display:inline-block; background-color:#ccc; background-size:cover;">

                                </div>
                            </a>
                        @endforeach
                    </div>
                </div>
            @endif
        </div>
    </div>
    <div class="   ">
        <div class="container ">
            <div class="row">
                <div class=" col-xs-12 text-center footer-links  ">
                    <ul class="list-inline   ">
{{--                        <li><a class="tr_link" data-trtype="footer_menu_plays" href="{{ route('onDemand.home') }}">Online παραστάσεις</a></li>--}}
{{--                        <li><a class="tr_link" data-trtype="footer_menu_plays" href="{{ route('plays.index',['scope'=>'recentlyStarted']) }}">Πρόγραμμα παραστάσεων</a></li>--}}
                        <li><a class="tr_link" data-trtype="footer_menu_interviews" href="{{ route('interviews.index') }}">{!! __('navigation.footer.interviews') !!}</a></li>
                        <li><a class="tr_link" data-trtype="footer_menu_news" href="{{ route('news.index') }}">{!! __('navigation.footer.news') !!}</a></li>
                        <li><a class="tr_link" data-trtype="footer_menu_people" href="{{ route('people.index') }}">{!! __('navigation.footer.people') !!}</a></li>
                        <li><a class="tr_link" data-trtype="footer_menu_about" href="{{ route('about.index') }}">{!! __('navigation.footer.about') !!}</a></li>
                        <li><a class="tr_link" data-trtype="footer_menu_contact" href="{{ route('contact.show') }}">{!! __('navigation.footer.contact') !!}</a></li>
                        <li><a class="tr_link" data-trtype="footer_menu_terms" href="{{ route('terms.index') }}">{!! __('navigation.footer.terms') !!}</a></li>
                    </ul>
                    <ul class="list-inline   ">
                        <li><a class="tr_link" data-trtype="footer_menu_pro" href="{{ route('neptune.pro.landing') }}">{!! __('navigation.footer.pro') !!}</a></li>
                        <li><a class="tr_link" data-trtype="footer_menu_facebook" href="https://www.facebook.com/unstagegr" target="_blank">{!! __('navigation.footer.facebook') !!}</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    <div class=" copyright ">
        <div class="container  ">
            <div class="row">
                <div class=" col-xs-12 text-center  ">
                    <span>unstage Copyright 2015 - {{ \Carbon\Carbon::now()->year }}</span>
                </div>
            </div>
        </div>
    </div>
</footer>
