@extends('components.layout')

@section('content')
    <div class="pages-header ">
        <div class="container container-narrow">
            <div class="row">
                <div class="col-sm-9  ">
                    @if($school)
                        <h1 class="schools__name">{!! $school->name !!}
                            @endif
                            <div class="schools__area">
                                {{ $school->area }}
                            </div>
                        </h1>
                        @admincan('manage_theatres')
                        <a href="{{ url('/uranus/schools', [$school->id, 'edit']) }}">Επεξεργασία</a>
                        @endadmincan
                </div>
                <div class="col-sm-3 text-md-right ">
                    @if(isset($school->facebook ) && $school->facebook != '')
                        <a href="{!! $school->facebook !!}" target="_blank"
                           class=" school__social">
                            <i class="fa fa-facebook-square  "
                               aria-hidden="true"></i>
                        </a>
                    @endif
                    @if(isset($school->twitter ) && $school->twitter != '')
                        <a href="{!! $school->twitter !!}" class=" school__social"
                           target="_blank">
                            <i class="fa fa-twitter-square "></i> </a>
                    @endif
                </div>
            </div>
        </div>
    </div>
    <div class="container  container-narrow">
        <div class="row">
            <div class="col-md-12">
                <div class="    ">
                    @if(isset($school->fullAddress ) && $school->fullAddress != '')
                    <div>
                        <i class="fa fa-map-marker"></i> {{ $school->fullAddress }}
                    </div>
                    @endif
                    @if(isset($school->phone ) && $school->phone != '')
                        <i class="fa fa-phone"></i> {!! $school->phone !!}
                        <br/>
                    @endif
                    @if(isset($school->mobile_phone ) && $school->mobile_phone != '')
                        <i class="fa fa-phone"></i> {!! $school->mobile_phone !!}
                        <br/>
                    @endif
                    @if(isset($school->website ) && $school->website != '' )
                        <a href="{{ $school->website }}" class=" " target="_blank">
                            <i class="fa fa-external-link-square"></i> Ιστοσελίδα</a>
                    @endif
                </div>
                @if($school->description)
                    <div class="schools__description">{!! \App\Helpers\ContentSanitizer::sanitizeHtml($school->description) !!}</div>
                @endif

                @if($alumni->count() > 0)
                    <h2>{{ trans('schools.show_alumni.alumni_heading') }}</h2>
                    <div class="row equal">
                        @foreach($alumni as $person)
                            <div class="col-md-6 col-lg-6 col-xs-12 col-ms-3 col-sm-6">
                                <a href="{{ route('people.show', $person->slug) }}">

                                    <div class="schoolAlumnus__name">
                                        {{ $person->fullName }}
                                        @if($person->pivot->year > 0)
                                            ({{ $person->pivot->year }})
                                        @endif
                                    </div>
                                </a>
                            </div>
                        @endforeach
                    </div>
                    {!! $alumni->links() !!}
                @endif
            </div>
        </div>
    </div>
@stop
