<?php

namespace Packages\Neptune\app;

use App\Components\Request;
use Illuminate\Support\ServiceProvider;
use Route;
use Validator;

class NeptuneServiceProvider extends ServiceProvider
{
    /**
     * This namespace is applied to your controller routes.
     *
     * In addition, it is set as the URL generator's root namespace.
     *
     * @var string
     */
    protected $namespace = 'Packages\Neptune\app\Http\Controllers';

    /**
     * Bootstrap the application services.
     *
     * @return void
     */
    public function boot()
    {
        // Preventing booting based on request (Request::isUSer() will work properly
        // for frontend but will fail will trying to send verification, password reset
        // and any other email requiring lang items from package
        // todo uncomment when dummy routes
        //if ( ! Request::isNeptune()) {
        //    return;
        //}
        $this->loadViewsFrom(__DIR__ . '/../resources/views', 'neptune');

        $this->loadTranslationsFrom(__DIR__ . '/../resources/lang', 'neptune');

        Validator::extend('old_user_password', function ($attribute, $value, $parameters) {
            return app('hash')->check($value, auth()->user()->getAuthPassword());
        });

        $this->registerRoutes();
    }

    /**
     * Register routes.
     *
     * @return void
     */
    public function registerRoutes()
    {
        // Set the locale for the application (legacy compatibility)
        setUnstageLocale();

        // Routes must be always registered for links in views and emails
        Route::group([
            'middleware' => ['web', 'setlocale', 'auth.neptune'],
            'prefix' => (app()->getLocale() !== config('locale.default_locale') ? app()->getLocale() . '/' : '') . config('neptune.app.prefix'),
            'namespace'  => $this->namespace,
        ], function () {
            require base_path('packages/Neptune/routes/web.neptune.php');
        });

        // Routes must be always registered and pro users for links in views and emails
        Route::group([
            'middleware' => ['web', 'auth.neptune', 'auth.neptune.pro'],
            'prefix' => (app()->getLocale() !== config('locale.default_locale') ? app()->getLocale() . '/' : '') . config('neptune.app.prefix_pro_app'),
            'namespace'  => $this->namespace,
        ], function () {
            require base_path('packages/Neptune/routes/web.neptune.pro.php');
        });

        // Routes must be always registered for links in views and emails
        Route::group([
            'middleware' => ['web'],
            'prefix' => (app()->getLocale() !== config('locale.default_locale') ? app()->getLocale() . '/' : '') . config('neptune.app.prefix_pro'),
            'namespace'  => $this->namespace,
        ], function () {
            require base_path('packages/Neptune/routes/web.neptune.pro.open.php');
        });
    }
}
