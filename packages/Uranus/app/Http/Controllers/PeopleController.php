<?php

namespace Packages\Uranus\app\Http\Controllers;

use App\Models\Album;
use App\Models\Book;
use App\Models\Image;
use App\Models\Person;
use App\Models\School;
use App\Models\Award;
use App\Models\SlugRedirect;
use App\Services\Search\Text\TextSearchService;
use App\Trak\Cache\InvalidatorPersonCache;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Http\Request;
use Packages\Neptune\app\Models\Subscription;
use Packages\Uranus\app\Http\Requests\PersonApiStoreRequest;
use Packages\Uranus\app\Http\Requests\PersonStoreRequest;
use Packages\Uranus\app\Http\Requests\PersonUpdateRequest;
use Packages\Uranus\app\Http\Requests\PersonTranslatableRequest;
use Artesaos\SEOTools\Facades\SEOMeta;
use Packages\Uranus\app\Http\Requests\SlugRedirectUpdateRequest;

class PeopleController extends Controller
{

    /**
     * @var TextSearchService
     */
    private $textSearch;

    /**
     * @var InvalidatorPersonCache
     */
    private $cacheInvalidator;


    public function __construct(TextSearchService $textSearch, InvalidatorPersonCache $cacheInvalidator)
    {
        parent::__construct();

        $this->cacheInvalidator = $cacheInvalidator;
        $this->textSearch       = $textSearch;
        $this->textSearch->setPreferredSearchLayer('eloquent');
    }


    /**
     * Display a listing of the resource.
     *
     * @param Request $request
     * @throws AuthorizationException
     */
    public function index(Request $request)
    {
        $this->authorizeForUser(auth('admin')->user(), 'see_people');

        if ($request->has('q')) {
            $textSearchDTO = $this->textSearch->setQuery($request->input('q'))
                                              ->setLimit(config('uranus.limits.people.searchResults'))
                                              ->people();
            $people = $textSearchDTO->people;
        }
        else {
            $sortBy = $request->input('sortBy', 'updated_at');
            $direction = $request->input('direction', 'desc');
            $page = $request->input('page', 1);
            $limit = config('uranus.limits.people.index');
            $offset = ($page * $limit) - $limit;

            // Init
            $people = Person::orderBy($sortBy, $direction);
            // If only non translated
            if($request->has('t') && $request->input('t') === 'notTranslated')
            {
                $people = $people->notTranslatedIn('en');
            }

            $people = $people
                            ->offset($offset)
                            ->with('mainImage')
                            ->limit($limit)
                            ->paginate();

            $people->setPath($request->url())
                   ->appends('sortBy', $sortBy)
                   ->appends('direction', $direction);

        }

        // load relations for all returned peeps
        $people->each(function($person) {
            $person->load([
                'plays' => function ($query) {
                    $query->groupBy('id')
                        ->orderBy('start_date', 'DESC');
                },
                'movies' => function ($query) {
                    $query->groupBy('id')
                        ->orderBy('year', 'DESC');
                },
                'tvShows' => function ($query) {
                    $query->groupBy('id')
                        ->orderBy('year', 'DESC');
                },
                'endeavours' => function ($query) {
                    $query->groupBy('id')
                        ->orderBy('year', 'DESC');
                },
            ]);
        });

        // Get all people
        $allPeople = Person::count();

        // Get not translated people count
        $notTranslated = Person::notTranslatedIn('en')->count();

        // seo title
        SEOMeta::setTitle('Index people');

        return view('uranus::people.index', compact('people', 'allPeople', 'notTranslated'));

    }


    /**
     * Show the form for creating a new resource.
     *
     * @throws AuthorizationException
     */
    public function create()
    {
        $this->authorizeForUser(auth('admin')->user(), 'manage_people');

        // List of people for autocomplete suggestions
        $people = Person::select(['first_name', 'last_name'])
                        ->get();
        $people = $people->pluck('fullname')->all();

        // seo title
        SEOMeta::setTitle('New person');

        return view('uranus::people.create', compact('people'));
    }


    /**
     * Store a newly created resource in storage.
     *
     * @param PersonStoreRequest $request
     * @throws AuthorizationException
     */
    public function store(PersonStoreRequest $request)
    {
        $this->authorizeForUser(auth('admin')->user(), 'manage_people');

        $input = array_map('trim', $request->all());

        $person = Person::create($input);

        // create the translations
        $person->translateOrNew('en')->first_name   = $input['first_name_en'];
        $person->translateOrNew('en')->last_name    = $input['last_name_en'];
        $person->save();

        // redirect to new person in case the save and new button was clicked
        if (isset($input['and_new']) && $input['and_new'] == '1')
        {
            return redirect()->route('uranus.people.create')
                ->with('success', 'Person ' . $person->id . ' added');

        }

        return redirect()->route('uranus.people.edit', [$person->id])
                         ->with('success', 'Person ' . $person->id . ' added');
    }


    /**
     * Store a newly created resource in storage via an API call.
     *
     * @param PersonApiStoreRequest $request
     * @throws AuthorizationException
     */
    public function apiStore(PersonApiStoreRequest $request)
    {
        $this->authorizeForUser(auth('admin')->user(), 'manage_people');

        $input = array_map('trim', $request->all());

        $data['first_name'] = $input['pc_first_name'];
        $data['last_name']  = $input['pc_last_name'];
        $data['sex']        = $input['pc_sex'];

        $person = Person::create($data);

        // handle translations
        $first_name_translation   = ucwords(\App\Components\Slug::create($data['first_name'], ' '));
        $last_name_translation    = ucwords(\App\Components\Slug::create($data['last_name'], ' '));

        // create the translations
        $person->translateOrNew('en')->first_name   = $first_name_translation;
        $person->translateOrNew('en')->last_name    = $last_name_translation;
        $person->save();

        $status = $person ? 1 : 0;
        $message = $person ? 'Ο συντελεστής με id ' . $person->id . ' δημιουργήθηκε επιτυχώς' : 'Σφάλμα κατά την δημιουργία του συντελεστή';

        return response()->json([
            'status'  => $status,
            'message' => $message,
        ]);
    }


    /**
     * Show the form for editing the specified resource.
     *
     * @param int $id
     * @throws AuthorizationException
     */
    public function edit($id)
    {
        $this->authorizeForUser(auth('admin')->user(), 'manage_people');

        // todo: do not load them all, instead make AJAX calls to search (as in people in plays)
        $data['schools']            = School::all();
        $data['subscriptions']      = Subscription::all();
        $data['awards']             = Award::all();

        $data['albums']             = Album::all();
        $data['books']              = Book::all();

        // Images
        $data['resourceType']       = 'person';
        $data['resourceTypeId']     = $id;

        $data['person'] = Person::where('id', $id)
                                ->with(['allQuotes', 'allTrivia', 'allAliases', 'telephones', 'emails', 'schools', 'awards', 'albums', 'books'])
                                ->firstOrFail();

        // seo title
        SEOMeta::setTitle('Edit person ' . $data['person']->fullName);

        return view('uranus::people.edit', $data);
    }


    /**
     * Show the form for editing the specified resource.
     *
     * @param int $id
     * @throws AuthorizationException
     */
    public function editReslug($id)
    {
        $this->authorizeForUser(auth('admin')->user(), 'manage_pro');

        $data['person'] = Person::where('id', $id)->firstOrFail();

        // seo title
        SEOMeta::setTitle('Edit person slug ' . $data['person']->fullName);

        return view('uranus::people.editReslug', $data);
    }


    /**
     * Show the form for editing the specified resource.
     *
     * @param int $id
     * @return Response
     * @throws AuthorizationException
     */
    public function editTranslatable($id)
    {
        $this->authorizeForUser(auth('admin')->user(), 'manage_people');

        $data['person'] = Person::where('id', $id)->firstOrFail();

        return view('uranus::people.editTranslatable', $data);
    }


    /**
     * Update the specified resource in storage.
     *
     * @param PersonUpdateRequest $request
     * @param int $id
     * @throws AuthorizationException
     */
    public function update(PersonUpdateRequest $request, $id)
    {
        $this->authorizeForUser(auth('admin')->user(), 'manage_people');

        $input = [];

        // trim all request vars, except the arrays
        foreach ($request->all() as $attribute => $value)
        {
            if (is_string($value))
            {
                $input[$attribute] = trim($value);
            }
            elseif (is_null($value))
            {
                $input[$attribute] = '';
            }
            else
            {
                $input[$attribute] = $value;
            }
        }

        $person = Person::findOrFail($id);

        $person->update($input);

        // hack to handle the removal of a subscription from a person
        // normally the variable subscription_id holds the ID of the linked (or the to-be linked) subscription
        // however if the variable remove_subscription is present with value equal to 1, we disassociate all subscriptions
        // form person
        if ($request->input('remove_subscription') == 1)
        {
            $person->subscription_id = null;
            $person->save();
        }

        $person->schools()->sync(($request->input('school_list') ? : []));
        $person->awards()->sync(($request->input('award_list') ? : []));
        $person->albums()->sync(($request->input('album_list') ? : []));
        $person->books()->sync(($request->input('book_list') ? : []));

        // invalidate this person's cached data
        $this->cacheInvalidator->invalidate($person, 'all');

        // redirect to edit page top or to edit page crm section based on
        // which submit button was clicked
        // if the define or remove submit buttons were clicked we redirect to the crm section
        if ($request->has('remove_subscription') || $request->has('define_subscription'))
        {
            session()->flash('success', 'Person\'s ' . $person->id . ' subscriptions updated');
            return redirect()->to(route('uranus.people.edit', [$person->id]) . '#crm');
        }

        // else we redirect to the normal edit page top
        return redirect()->route('uranus.people.edit', [$person->id])
                         ->with('success', 'Person ' . $person->id . ' updated');
    }


    /**
     * Update the specified resource in storage.
     *
     * @param SlugRedirectUpdateRequest $request
     * @param int $id
     * @throws AuthorizationException
     */
    public function updateReslug(SlugRedirectUpdateRequest $request, $id)
    {
        $this->authorizeForUser(auth('admin')->user(), 'manage_pro');

        $input = array_map('trim', $request->all());

        // find the person
        $person = Person::findOrFail($id);

        // initially create the slug redirection rule
        $slugRedirect = SlugRedirect::create($input);

        // ... and then change the slug of the person
        $person->slug = $slugRedirect->new_slug;
        $person->save();

        // invalidate this person's cached data
        $this->cacheInvalidator->invalidate($person, 'all');

        return redirect()->route('uranus.people.reslug.edit', [$person->id])
                         ->with('success', 'Person ' . $person->id . ' had their slug updated and redirection rule created in db');
    }


    /**
     * Update the specified resource in storage.
     *
     * @param PersonTranslatableRequest $request
     * @param int $id
     * @throws AuthorizationException
     */
    public function updateTranslatable(PersonTranslatableRequest $request, $id)
    {
        $this->authorizeForUser(auth('admin')->user(), 'manage_people');

        $input = [];

        // trim all request vars, except the arrays
        foreach ($request->all() as $attribute => $value)
        {
            if (is_string($value))
            {
                $input[$attribute] = trim($value);
            }
            elseif (is_null($value))
            {
                $input[$attribute] = '';
            }
            else
            {
                $input[$attribute] = $value;
            }
        }

        $person = Person::findOrFail($id);

        // Add translatable data
        $person->translateOrNew('en')->first_name           = $input['first_name_en'];
        $person->translateOrNew('en')->last_name            = $input['last_name_en'];
        $person->translateOrNew('en')->bio                  = $input['bio_en'];
        $person->translateOrNew('en')->auto_biography_text  = $input['auto_biography_text_en'];
        $person->translateOrNew('en')->living_place         = $input['living_place_en'];
        $person->translateOrNew('en')->birth_place          = $input['birth_place_en'];
        $person->translateOrNew('en')->death_place          = $input['death_place_en'];
        $person->save();

        // invalidate this person's cached data
        $this->cacheInvalidator->invalidate($person, 'all');

        return redirect()->route('uranus.people.translatable.edit', [$person->id])
                         ->with('success', 'Person ' . $person->id . ' updated');
    }


    /**
     * Remove the specified resource from storage.
     *
     * @param int $id
     * @throws AuthorizationException
     */
    public function destroy($id)
    {
        $this->authorizeForUser(auth('admin')->user(), 'delete_people');

        //todo create query to delete images where theatric_play_id == id
        $person = Person::where('id', $id)->with('images')->firstOrFail();

        // delete each image entry related to this play from images table
        foreach ($person->images as $image) {
            Image::destroy($image->id);
            // Todo decide how we are going to handle tha actual delete of file
        }

        // invalidate this person's cached data
        $this->cacheInvalidator->invalidate($person, 'all');

        $deleted = Person::destroy($id);

        $status = $deleted ? 1 : 0;
        $message = $deleted ? 'Ο συντελεστής με id ' . $id . ' διαγράφηκε επιτυχώς' : 'Σφάλμα κατά την διαγραφή του συντελεστή';


        return response()->json([
            'status'  => $status,
            'message' => $message,
        ]);
    }


    public function getPersonList(Request $request)
    {
        if ($request->has('getPeople')) {
            $image = Image::where('filename', $request->filename)->firstOrFail();

            // check whether we want to get people tagged or credited to an image
            // this is the credited people case
            if($request->has('for_credits'))
            {
                return Person::join('image_person', 'people.id', '=', 'image_person.person_id')
                    ->where('image_id', '=', $image->id)
                    ->where('image_person.role', 'credit')
                    ->select('first_name', 'last_name', 'people.id')
                    ->get()->pluck('id', 'full_name');
            }

            // this is the tagged people case
            return Person::join('image_person', 'people.id', '=', 'image_person.person_id')
                ->where('image_id', '=', $image->id)
                ->where('image_person.role', 'tag')
                ->select('first_name', 'last_name', 'people.id')
                ->get()->pluck('id', 'full_name');

        }

        if ($request->has('searchPeople')) {
            $paginator = $this->textSearch->setQuery($request->input('query'))->people();

            $people = [];
            if ($paginator->people) {
                $peopleDTOS = $paginator->people->all();
                foreach ($peopleDTOS as $dto) {
                    $people[$dto->fullName] = $dto->id;
                }
            }

            return $people;
        }

    }

}
