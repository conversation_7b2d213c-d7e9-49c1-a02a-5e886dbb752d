<?php namespace Packages\Uranus\app\Http\Controllers;

use App\Models\Movie;
use App\Models\FilmGenre;
use App\Models\Tag;
use App\Services\Search\Text\TextSearchService;
use Artesaos\SEOTools\Facades\SEOMeta;
use Illuminate\Http\Request;
use Packages\Uranus\app\Http\Requests\MovieRequest;

class MoviesController extends Controller
{

    /**
     * @var TextSearchService
     */
    private $textSearch;

    public function __construct(TextSearchService $textSearch)
    {
        parent::__construct();

        $this->textSearch = $textSearch;
        $this->textSearch->setPreferredSearchLayer('eloquent');
    }

    /**
     * Display a listing of the resource.
     *
     */
    public function index(Request $request)
    {
        $this->authorizeForUser(auth('admin')->user(), 'see_plays');

        // if we have a query string then search plays by this string
        if ($request->has('q')) {
            $textSearchDTO = $this->textSearch
                ->setQuery($request->input('q'))
                ->setLimit(config('uranus.limits.plays.searchResults'))
                ->movies();
            $movies = $textSearchDTO->movies;
        }
        else {
            $sortBy = $request->input('sortBy', 'created_at');
            // Prevent sorting by translatable fields that no longer exist in main table
            if (in_array($sortBy, ['title', 'synopsis'])) {
                $sortBy = 'created_at';
            }
            $direction = $request->input('direction', 'desc');
            $page = $request->input('page', 1);
            $limit = config('uranus.limits.plays.index');
            $offset = ($page * $limit) - $limit;

            // Init
            $movies = Movie::orderBy($sortBy, $direction);
            // If only non translated
            if($request->has('t') && $request->input('t') === 'notTranslated')
            {
                $movies = $movies->notTranslatedIn('en');
            }

            $movies = $movies
                ->offset($offset)
                ->limit($limit)
                ->paginate();

            $movies->setPath($request->url())
                ->appends('sortBy', $sortBy)
                ->appends('direction', $direction);

            // Preserve the translation filter parameter
            if($request->has('t')) {
                $movies->appends('t', $request->input('t'));
            }
        }

        // Get all movies
        $allMovies = Movie::count();
        // Get not translated movies count
        $notTranslated = Movie::notTranslatedIn('en')->count();

        // seo title
        SEOMeta::setTitle('Index movies');

        return view('uranus::movies.index', compact('movies', 'allMovies', 'notTranslated'));
    }

    /**
     * Display a listing of the unfinalised resource.
     *
     */
    public function indexUnfinalised(Request $request)
    {
        $this->authorizeForUser(auth('admin')->user(), 'see_plays');

        // if we have a query string then search plays by this string
        $sortBy = $request->input('sortBy', 'created_at');
        // Prevent sorting by translatable fields that no longer exist in main table
        if (in_array($sortBy, ['title', 'synopsis'])) {
            $sortBy = 'created_at';
        }
        $direction = $request->input('direction', 'desc');
        $page = $request->input('page', 1);
        $limit = config('uranus.limits.plays.index');
        $offset = ($page * $limit) - $limit;

        // Init
        $movies = Movie::orderBy($sortBy, $direction)
            ->where('finalised', 0);

        $movies = $movies
            ->offset($offset)
            ->limit($limit)
            ->paginate();

        $movies->setPath($request->url())
            ->appends('sortBy', $sortBy)
            ->appends('direction', $direction);

        // Get all movies
        $allMovies = Movie::count();

        // seo title
        SEOMeta::setTitle('Index unfinalised movies');

        return view('uranus::movies.index_unfinalised', compact('movies', 'allMovies'));
    }


    /**
     * Show the list of user saved movies
     *
     * @param Request $request
     * @return mixed
     * @throws AuthorizationException
     */
    public function indexUserSaved(Request $request)
    {
        $this->authorizeForUser(auth('admin')->user(), 'see_plays');

        $sortBy = $request->input('sortBy', 'updated_at');
        // Prevent sorting by translatable fields that no longer exist in main table
        if (in_array($sortBy, ['title', 'synopsis'])) {
            $sortBy = 'updated_at';
        }
        $direction = $request->input('direction', 'desc');
        $page = $request->input('page', 1);
        $limit = config('uranus.limits.plays.index');
        $offset = ($page * $limit) - $limit;

        // Init
        $movies = Movie::whereNotNull('user_id')
            ->orderBy($sortBy, $direction);

        // handle free text query string
        $query_q = $request->query('q');
        if ( !empty($query_q) )
        {
            $movies->where(function ($query) use ($query_q) {
                $query->orWhereHas('translations', function ($translationQuery) use ($query_q) {
                        $translationQuery->where('title', 'LIKE', '%' . $query_q . '%');
                    })
                    ->orWhere('notes', 'LIKE', '%' . $query_q . '%')
                    ->orWhere('user_notes', 'LIKE', '%' . $query_q . '%');
            });
        }

        // handle moderated query string
        $query_moderated = $request->query('moderated');
        if( !empty($query_moderated) )
        {
            if ($query_moderated == 'moderated')
            {
                $movies->where('moderated', true);
            }
            elseif ($query_moderated == 'unmoderated')
            {
                $movies->where('moderated', false);
            }
        }

        // handle published query string
        $query_published = $request->query('published');
        if( !empty($query_published) )
        {
            if ($query_published == 'published')
            {
                $movies->where('published', true);
            }
            elseif ($query_published == 'unpublished')
            {
                $movies->where('published', false);
            }
        }

        // handle user_id query string
        $query_user_id = $request->query('user_id');
        if( !empty($query_user_id) )
        {
            $movies->where('user_id', $query_user_id);
        }

        $movies = $movies
            ->offset($offset)
            ->limit($limit)
            ->paginate();

        $movies->setPath($request->url());

        // append q query string if present
        if ( !empty($query_q) )
        {
            $movies->appends('q', $query_q);
        }

        // append moderated query string if present
        if ( !empty($query_moderated) && ($query_moderated == 'moderated' || $query_moderated == 'unmoderated'))
        {
            $movies->appends('moderated', $query_moderated);
        }

        // append published query string if present
        if ( !empty($query_published) && ($query_published == 'published' || $query_published == 'unpublished'))
        {
            $movies->appends('published', $query_published);
        }

        // append user_id query string if present
        if ( !empty($query_user_id) )
        {
            $movies->appends('user_id', $query_user_id);
        }

        $movies->appends('sortBy', $sortBy)
            ->appends('direction', $direction);

        // load relations and extra data for all returned models
        $movies->each(function($movie) {
            $movie->load([
                'user',
            ]);
        });

        // seo title
        SEOMeta::setTitle('Index user saved movies');

        return view('uranus::movies.index_user_saved', compact('movies'));
    }


    /**
     * Show the form for creating a new resource.
     *
     */
    public function create()
    {
        $this->authorizeForUser(auth('admin')->user(), 'manage_plays');

        // List of movies for autocomplete suggestions
        $movies = Movie::with('translations')->get();
        $movies = $movies->pluck('title')->filter()->all();

        // seo title
        SEOMeta::setTitle('New movie');

        return view('uranus::movies.create', compact('movies'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param MovieRequest $request
     * @throws AuthorizationException
     */
    public function store(MovieRequest $request)
    {
        $this->authorizeForUser(auth('admin')->user(), 'manage_plays');

        // Create Movie with non-translatable fields first
        $movie = Movie::create(['year' => trim($request->year)]);

        // Set translatable fields and save them first
        $movie->translateOrNew('el')->title = trim($request->title);
        $movie->save(); // This saves both the model and translations

        // Force regenerate slug now that translation is saved
        $movie->slug = null; // Clear existing slug
        $movie->save(); // Regenerate slug with proper title

        return redirect()->route('uranus.movies.edit', [$movie->id])
            ->with('success', 'Η ταινία με id ' . $movie->id . ' δημιουργήθηκε επιτυχώς');
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param int $id
     * @throws AuthorizationException
     */
    public function edit($id)
    {
        $this->authorizeForUser(auth('admin')->user(), 'manage_plays');

        $movie = Movie::where('id', $id)->with(['filmGenres', 'roles', 'user', 'translations'])->firstOrFail();
        $movie->load([
            'roles.moviePeople' => function ($query) use ($movie) {
                $query->wherePivot('movie_id', '=', $movie->id);
            },
        ]);
        $data['movie']      = $movie;
        $data['filmGenres'] = FilmGenre::all();
        $data['tags']       = Tag::all();

        $rolesWithoutActors = [];
        // Workaround to get list of roles for which this movie
        // doesn't have any person
        $rolesIdsWithActors = [];
        foreach ($data['movie']->roles as $movieRole) {
            $rolesIdsWithActors[] = $movieRole->id;
        }
        foreach (config('roles')->where('for_movies', true) as $role) {
            if ( ! in_array($role->id, $rolesIdsWithActors)) {
                $rolesWithoutActors[] = $role;
            }
        }

        $data['rolesWithoutActors'] = $rolesWithoutActors;

        // Images
        $data['resourceType']       = 'movie';
        $data['resourceTypeId']     = $id;

        // seo title
        SEOMeta::setTitle('Edit movie ' . $movie->title);

        return view('uranus::movies.edit', $data);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param MovieRequest $request
     * @param int $id
     * @throws AuthorizationException
     */
    public function update(MovieRequest $request, $id)
    {

        $this->authorizeForUser(auth('admin')->user(), 'manage_plays');

        $movie = Movie::findOrFail($id);

        $input = [];

        // trim all request vars, except the arrays
        foreach ($request->all() as $attribute => $value)
        {
            if (is_string($value))
            {
                $input[$attribute] = trim($value);
            }
            elseif (is_null($value))
            {
                $input[$attribute] = '';
            }
            else
            {
                $input[$attribute] = $value;
            }
        }

        $input['published'] = isset($input['published']) ? 1 : 0;
        $input['finalised'] = isset($input['finalised']) ? 1 : 0;

        // we update in two steps in order to check if an attribute has been changed
        $movie->fill($input);

        // Handle translatable fields (Greek)
        if (isset($input['title'])) {
            $movie->translateOrNew('el')->title = $input['title'];
        }
        if (isset($input['synopsis'])) {
            $movie->translateOrNew('el')->synopsis = $input['synopsis'];
        }

        // and finaly save the model to persist tha changes
        $movie->save();

        $movie->filmGenres()->sync(($request->input('film_genre_list') ? : []));

        $movie->tags()->sync(($request->input('tag_list') ? : []));

        return redirect()->route('uranus.movies.edit', [$movie->id])
            ->with('success', 'Movie ' . $movie->id . ' updated');
    }

    /**
     * Show the form for editing the specified resource translations.
     *
     * @param Movie $movie
     * @return \Illuminate\Http\Response
     */
    public function editTranslatable(Movie $movie)
    {
        $this->authorizeForUser(auth('admin')->user(), 'manage_plays');

        return view('uranus::movies.editTranslatable', compact('movie'));
    }

    /**
     * Update the specified resource translations in storage.
     *
     * @param Request $request
     * @param Movie $movie
     * @return \Illuminate\Http\Response
     */
    public function updateTranslatable(Request $request, Movie $movie)
    {
        $this->authorizeForUser(auth('admin')->user(), 'manage_plays');

        $input = $request->all();

        // Add translatable data
        $movie->translateOrNew('en')->title = $input['title_en'];
        $movie->translateOrNew('en')->synopsis = $input['synopsis_en'];

        // Save the model to persist the changes
        $movie->save();

        return redirect()->route('uranus.movies.translatable.edit', [$movie->id])
                         ->with('success', 'Movie ' . $movie->id . ' translations updated');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param $id
     */
    public function destroy($id)
    {
        $this->authorizeForUser(auth('admin')->user(), 'delete_plays');

        $deleted = Movie::destroy($id);

        $status = $deleted ? 1 : 0;
        $message = $deleted ? 'Η ταινία με id ' . $id . ' διαγράφηκε επιτυχώς' : 'Σφάλμα κατά την διαγραφή της ταινίας';

        return response()->json([
            'status'  => $status,
            'message' => $message,
        ]);
    }

    /*
     * Used to return movies for use in select2 dropdowns input
     */
    public function getMovieList(Request $request)
    {
        if ($request->has('searchMovies'))
        {
            $paginator = $this->textSearch->setQuery($request->input('query'))->movies();

            $movies = [];
            if ($paginator->movies)
            {
                $movieDTOS = $paginator->movies->all();
                foreach ($movieDTOS as $dto)
                {
                    $movies[$dto->title . ' (' . $dto->year .') (id: ' . $dto->id . ')'] = $dto->id;
                }
            }

            return $movies;
        }

    }

}
