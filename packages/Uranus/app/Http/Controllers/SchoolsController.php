<?php namespace Packages\Uranus\app\Http\Controllers;

use App\Models\Alumnus;
use App\Models\School;
use Artesaos\SEOTools\Facades\SEOMeta;
use Illuminate\Http\Request;
use Packages\Uranus\app\Http\Requests\SchoolRequest;
use Packages\Uranus\app\Http\Requests\SchoolTranslatableRequest;

class SchoolsController extends Controller
{

    /**
     * Display a listing of the resource.
     *
     */
    public function index(Request $request)
    {
        $this->authorizeForUser(auth('admin')->user(), 'see_theatres');

        $sortBy = $request->input('sortBy', 'updated_at');
        // Prevent sorting by translatable fields that no longer exist in main table
        if (in_array($sortBy, ['name', 'description'])) {
            $sortBy = 'updated_at';
        }
        $direction = $request->input('direction', 'desc');
        $page = $request->input('page', 1);
        $limit = config('uranus.limits.schools.index');
        $offset = ($page * $limit) - $limit;

        if ($request->has('q'))
        {
            $query = $request->input('q');

            // Search in translations
            $schools = School::whereTranslationLike('name', '%' . $query . '%')
                ->orderBy($sortBy, $direction)
                ->offset($offset)
                ->limit($limit)
                ->paginate();
        }
        else
        {
            // Init
            $schools = School::orderBy($sortBy, $direction);
            // If only non translated
            if($request->has('t') && $request->input('t') === 'notTranslated')
            {
                $schools = $schools->notTranslatedIn('en');
            }

            $schools = $schools
                ->offset($offset)
                ->limit($limit)
                ->paginate();
        }

        $schools->setPath($request->url())
            ->appends('sortBy', $sortBy)
            ->appends('direction', $direction);

        // Get all schools
        $allSchools = School::count();

        // Get not translated schools count
        $notTranslated = School::notTranslatedIn('en')->count();

        // seo title
        SEOMeta::setTitle('Index schools');

        return view('uranus::schools.index', compact('schools', 'allSchools', 'notTranslated'));
    }

    /**
     * Show the form for creating a new resource.
     *
     */
    public function create()
    {
        $this->authorizeForUser(auth('admin')->user(), 'manage_theatres');

        // seo title
        SEOMeta::setTitle('New school');

        return view('uranus::schools.create');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param SchoolRequest $request
     */
    public function store(SchoolRequest $request)
    {
        $this->authorizeForUser(auth('admin')->user(), 'manage_theatres');

        $input = array_map('trim', $request->all());

        // Create School with non-translatable fields first
        $nonTranslatableFields = array_diff_key($input, array_flip(['name', 'description']));
        $school = School::create($nonTranslatableFields);

        // Set translatable fields and save them first
        $school->translateOrNew('el')->name = trim($request->name);
        if ($request->description) {
            $school->translateOrNew('el')->description = trim($request->description);
        }
        $school->save(); // This saves both the model and translations

        // Force regenerate slug now that translation is saved
        $school->slug = null; // Clear existing slug
        $school->save(); // Regenerate slug with proper name

        return redirect()->route('uranus.schools.edit', [$school->id])
                         ->with('success', 'School ' . $school->id . ' added');
    }


    /**
     * Show the form for editing the specified resource.
     *
     * @param $id
     */
    public function edit($id)
    {
        $this->authorizeForUser(auth('admin')->user(), 'manage_theatres');

        $school = School::findOrFail($id);

        // seo title
        SEOMeta::setTitle('Edit school ' . $school->name);

        return view('uranus::schools.edit', compact('school'));
    }


    /**
     * Show the form for editing the alumni of specified resource.
     *
     * @param $id
     */
    public function alumniEdit($id, Request $request)
    {
        $this->authorizeForUser(auth('admin')->user(), 'manage_theatres');

        $school = School::findOrFail($id);

        $sortBy = $request->input('sortBy', 'year');
        $direction = $request->input('direction', 'desc');
        $page = $request->input('page', 1);
        $limit = config('uranus.limits.schools.index');
        $offset = ($page * $limit) - $limit;

        $alumni = Alumnus::orderBy($sortBy, $direction)
            ->where('school_id', '=', $school->id);

        $query_year = $request->query('year');
        if ($query_year !== null)
        {
            $alumni->where('year', '=', $query_year);
        }

        $alumni = $alumni->offset($offset)
            ->limit($limit)
            ->paginate();

        $alumni->setPath($request->url());

        // append year query string if present
        if ($query_year !== null)
        {
            $alumni->appends('year', $query_year);
        }

        $alumni->appends('sortBy', $sortBy)
            ->appends('direction', $direction);

        return view('uranus::schools.alumniEdit', compact('school', 'alumni'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param SchoolRequest $request
     * @param $id
     * @internal param $id
     * @internal param int $id
     */
    public function update(SchoolRequest $request, $id)
    {
        $this->authorizeForUser(auth('admin')->user(), 'manage_theatres');

        $input = array_map('trim', $request->all());

        $school = School::findOrFail($id);

        // manual handling of the showable checkbox submission
        $input['showable'] = isset($input['showable']) ? 1 : 0;

        // Update non-translatable fields
        $nonTranslatableFields = array_diff_key($input, array_flip(['name', 'description']));
        $school->update($nonTranslatableFields);

        // Update translatable fields (hardcoded Greek language)
        $school->translateOrNew('el')->name = trim($request->name);
        if ($request->description) {
            $school->translateOrNew('el')->description = trim($request->description);
        }
        $school->save();

        return redirect()->route('uranus.schools.edit', [$school->id])
             ->with('success', 'School ' . $id . ' updated');
    }

    /**
     * Show the form for editing the translatable attributes of the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function editTranslatable($id)
    {
        $this->authorizeForUser(auth('admin')->user(), 'manage_theatres');

        $school = School::findOrFail($id);

        // seo title
        SEOMeta::setTitle('Edit school translations ' . $school->name);

        return view('uranus::schools.editTranslatable', compact('school'));
    }

    /**
     * Update the translatable attributes of the specified resource in storage.
     *
     * @param  SchoolTranslatableRequest  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function updateTranslatable(SchoolTranslatableRequest $request, $id)
    {
        $this->authorizeForUser(auth('admin')->user(), 'manage_theatres');

        $school = School::findOrFail($id);

        $input = [];

        // trim all request vars, except the arrays
        foreach ($request->all() as $attribute => $value)
        {
            if (is_string($value))
            {
                $input[$attribute] = trim($value);
            }
            elseif (is_null($value))
            {
                $input[$attribute] = '';
            }
            else
            {
                $input[$attribute] = $value;
            }
        }

        // Add translatable data
        $school->translateOrNew('en')->name = $input['name_en'];
        $school->translateOrNew('en')->description = $input['description_en'];

        // Save the model to persist the changes
        $school->save();

        return redirect()->route('uranus.schools.translatable.edit', [$school->id])
                         ->with('success', 'School ' . $school->id . ' translations updated');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param $id
     */
    public function destroy($id)
    {
        $this->authorizeForUser(auth('admin')->user(), 'delete_theatres');

        $deleted = School::destroy($id);

        $status = $deleted ? 1 : 0;
        $message = $deleted ? 'Το school με id ' . $id . ' διαγράφηκε επιτυχώς' : 'Σφάλμα κατά την διαγραφή του school';

        return response()->json([
            'status'  => $status,
            'message' => $message,
        ]);
    }


    /**
     * Attach a new person to a school
     *
     * @param Request $request
     */
    public function addPerson(Request $request)
    {
        $school_id  = $request['schoolId'];
        $person_id  = $request['personId'];
        $year       = $request['year'];

        // find school
        $school = School::findOrFail($school_id);

        if(!empty($year))
        {
            $school->people()->syncWithoutDetaching([
                $person_id => [
                    'year' => $year,
                ],
            ]);
        }
        else
        {
            $school->people()->syncWithoutDetaching($person_id);
        }
    }


    /**
     * Detach a person from a school
     *
     * @param Request $request
     */
    public function removePerson(Request $request)
    {
        if ($request->has('schoolId') && $request->has('personId'))
        {
            $school_id = $request['schoolId'];
            $person_id  = $request['personId'];

            // find school
            $school = School::findOrFail($school_id);

            $school->people()->detach($person_id);
        }
    }

}
