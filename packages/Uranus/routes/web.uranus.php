<?php

Route::get('/', 'DashboardController@index')->name('uranus.dashboard');

// =======================================================================//
//                               Theatres
// =======================================================================//
//Route::post('theatres/geocoding', 'TheatresController@geocoding')->name('uranus.geocoding');
Route::get('theatres', 'TheatresController@index')->name('uranus.theatres.index');
Route::get('theatres/create', 'TheatresController@create')->name('uranus.theatres.create');
Route::post('theatres', 'TheatresController@store')->name('uranus.theatres.store');
Route::get('theatres/{theatre}/edit', 'TheatresController@edit')->name('uranus.theatres.edit');
Route::get('theatres/{theatre}/editTranslatable', 'TheatresController@editTranslatable')->name('uranus.theatres.translatable.edit');
Route::put('theatres/{theatre}', 'TheatresController@update')->name('uranus.theatres.update');
Route::put('theatres/{theatre}/updateTranslatable', 'TheatresController@updateTranslatable')->name('uranus.theatres.translatable.update');
Route::delete('theatres/{theatre}', 'TheatresController@destroy')->name('uranus.theatres.destroy');

// =======================================================================//
//                               People
// =======================================================================//
Route::post('people/personList', 'PeopleController@getPersonList')->name('uranus.people.personList');
Route::get('people', 'PeopleController@index')->name('uranus.people.index');
Route::get('people/create', 'PeopleController@create')->name('uranus.people.create');
Route::post('people', 'PeopleController@store')->name('uranus.people.store');
Route::post('people/api-store', 'PeopleController@apiStore')->name('uranus.people.apiStore');
Route::get('people/{people}/edit', 'PeopleController@edit')->name('uranus.people.edit');
Route::get('people/{people}/editTranslatable', 'PeopleController@editTranslatable')->name('uranus.people.translatable.edit');
Route::get('people/{id}/editReslug', 'PeopleController@editReslug')->name('uranus.people.reslug.edit');
Route::put('people/{people}/updateTranslatable', 'PeopleController@updateTranslatable')->name('uranus.people.translatable.update');
Route::put('people/{id}/updateReslug', 'PeopleController@updateReslug')->name('uranus.people.reslug.update');
Route::put('people/{people}', 'PeopleController@update')->name('uranus.people.update');
Route::delete('people/{people}', 'PeopleController@destroy')->name('uranus.people.destroy');
Route::get('/people/{person}/castings', 'PeopleAnalyticsController@castings')->name('uranus.people.castings');
Route::get('/people/{person}/analytics', 'PeopleAnalyticsController@analytics')->name('uranus.people.analytics');
Route::get('/people/{person}/rankingHistory', 'PeopleRankingsController@history')->name('uranus.people.rankingHistory');
Route::get('/people/rankings', 'PeopleRankingsController@index')->name('uranus.people.rankings.index');

Route::post('people/translate', 'PeopleTranslationsController@suggest')->name('uranus.people.translation.suggest');


// =======================================================================//
//                               Pro People
// =======================================================================//
Route::get('people/proEnabled', 'PeopleProController@index')->name('uranus.people.proEnabled.index');
Route::get('people/{id}/proUsers', 'PeopleProController@editProUsers')->name('uranus.people.proUsers');
Route::get('/people/{id}/proData', 'PeopleProController@showProData')->name('uranus.people.proData');


// =======================================================================//
//                    Cast (Person at Play/Movie/TV Show/Endeavour with Role)
// =======================================================================//
// Pays
Route::post('personPlayRole/store', 'PeoplePlaysRolesController@store')->name('uranus.personPlayRole.store');
Route::post('personPlayRole/destroy', 'PeoplePlaysRolesController@destroy')->name('uranus.personPlayRole.destroy');
// Movies
Route::post('moviePersonRole/store', 'MoviesPeopleRolesController@store')->name('uranus.moviePersonRole.store');
Route::post('moviePersonRole/destroy', 'MoviesPeopleRolesController@destroy')->name('uranus.moviePersonRole.destroy');
// TvShows
Route::post('personRoleTvShow/store', 'PeopleRolesTvShowsController@store')->name('uranus.personRoleTvShow.store');
Route::post('personRoleTvShow/destroy', 'PeopleRolesTvShowsController@destroy')->name('uranus.personRoleTvShow.destroy');
// Endeavours
Route::post('endeavourPersonRole/store', 'EndeavoursPeopleRolesController@store')->name('uranus.endeavourPersonRole.store');
Route::post('endeavourPersonRole/destroy', 'EndeavoursPeopleRolesController@destroy')->name('uranus.endeavourPersonRole.destroy');

// =======================================================================//
//                               Festivals
// =======================================================================//
Route::get('festivals', 'FestivalsController@index')->name('uranus.festivals.index');
Route::get('festivals/create', 'FestivalsController@create')->name('uranus.festivals.create');
Route::post('festivals', 'FestivalsController@store')->name('uranus.festivals.store');
Route::get('festivals/{festival}/edit', 'FestivalsController@edit')->name('uranus.festivals.edit');
Route::put('festivals/{festival}', 'FestivalsController@update')->name('uranus.festivals.update');
Route::delete('festivals/{festival}', 'FestivalsController@destroy')->name('uranus.festivals.destroy');

// =======================================================================//
//                               Quotes
// =======================================================================//
Route::get('quotes/create', 'QuotesController@create')->name('uranus.quotes.create');
Route::post('quotes', 'QuotesController@store')->name('uranus.quotes.store');
Route::get('quotes/{quote}/edit', 'QuotesController@edit')->name('uranus.quotes.edit');
Route::put('quotes/{quote}', 'QuotesController@update')->name('uranus.quotes.update');
Route::delete('quotes/{quote}', 'QuotesController@destroy')->name('uranus.quotes.destroy');

// =======================================================================//
//                               Trivia
// =======================================================================//
Route::get('trivia/create', 'TriviaController@create')->name('uranus.trivia.create');
Route::post('trivia', 'TriviaController@store')->name('uranus.trivia.store');
Route::get('trivia/{trivia}/edit', 'TriviaController@edit')->name('uranus.trivia.edit');
Route::put('trivia/{trivia}', 'TriviaController@update')->name('uranus.trivia.update');
Route::delete('trivia/{trivia}', 'TriviaController@destroy')->name('uranus.trivia.destroy');

// =======================================================================//
//                               Person Aliases
// =======================================================================//
Route::get('personAliases/create', 'PersonAliasesController@create')->name('uranus.personAliases.create');
Route::post('personAliases', 'PersonAliasesController@store')->name('uranus.personAliases.store');
Route::get('personAliases/{alias}/edit', 'PersonAliasesController@edit')->name('uranus.personAliases.edit');
Route::put('personAliases/{alias}', 'PersonAliasesController@update')->name('uranus.personAliases.update');
Route::delete('personAliases/{alias}', 'PersonAliasesController@destroy')->name('uranus.personAliases.destroy');

// =======================================================================//
//                               Person Telephones
// =======================================================================//
Route::get('personTelephones/create', 'PersonTelephonesController@create')->name('uranus.personTelephones.create');
Route::post('personTelephones', 'PersonTelephonesController@store')->name('uranus.personTelephones.store');
Route::get('personTelephones/{personTelephone}/edit', 'PersonTelephonesController@edit')
     ->name('uranus.personTelephones.edit');
Route::put('personTelephones/{personTelephone}', 'PersonTelephonesController@update')
     ->name('uranus.personTelephones.update');
Route::delete('personTelephones/{personTelephone}', 'PersonTelephonesController@destroy')
     ->name('uranus.personTelephones.destroy');

// =======================================================================//
//                               Person Emails
// =======================================================================//
Route::get('personEmails/create', 'PersonEmailsController@create')->name('uranus.personEmails.create');
Route::post('personEmails', 'PersonEmailsController@store')->name('uranus.personEmails.store');
Route::get('personEmails/{personEmail}/edit', 'PersonEmailsController@edit')->name('uranus.personEmails.edit');
Route::put('personEmails/{personEmail}', 'PersonEmailsController@update')->name('uranus.personEmails.update');
Route::delete('personEmails/{personEmail}', 'PersonEmailsController@destroy')->name('uranus.personEmails.destroy');

// =======================================================================//
//                               Critic Reviews
// =======================================================================//
Route::get('plays/{play}/criticReviews', 'CriticReviewsController@index')->name('uranus.criticReviews.index');
Route::get('plays/{play}/criticReviews/create', 'CriticReviewsController@create')->name('uranus.criticReviews.create');
Route::post('plays/{play}/criticReviews', 'CriticReviewsController@store')->name('uranus.criticReviews.store');
Route::get('criticReviews/{criticReview}/edit', 'CriticReviewsController@edit')->name('uranus.criticReviews.edit');
Route::put('criticReviews/{criticReview}', 'CriticReviewsController@update')->name('uranus.criticReviews.update');
Route::delete('criticReviews/{criticReview}', 'CriticReviewsController@destroy')->name('uranus.criticReviews.destroy');

// =======================================================================//
//                               Referral Clicks
// =======================================================================//
Route::get('referralClicks', 'ReferralClicksController@index')->name('uranus.referralClicks.index');

// =======================================================================//
//                               Person References
// =======================================================================//
Route::get('people/{person}/personReferences', 'PersonReferencesController@index')->name('uranus.personReferences.index');
Route::get('people/{person}/personReferences/create', 'PersonReferencesController@create')->name('uranus.personReferences.create');
Route::post('people/{person}/personReferences', 'PersonReferencesController@store')->name('uranus.personReferences.store');
Route::get('personReferences/{personReference}/edit', 'PersonReferencesController@edit')->name('uranus.personReferences.edit');
Route::put('personReferences/{personReference}', 'PersonReferencesController@update')->name('uranus.personReferences.update');
Route::delete('personReferences/{personReference}', 'PersonReferencesController@destroy')->name('uranus.personReferences.destroy');

// =======================================================================//
//                               Plays
// =======================================================================//
Route::post('plays/playList', 'Plays\PlaysController@getPlayList')->name('uranus.plays.playList');

Route::get('plays', 'Plays\PlaysController@index')->name('uranus.plays.index');
Route::get('plays/create', 'Plays\PlaysController@create')->name('uranus.plays.create');
Route::post('plays', 'Plays\PlaysController@store')->name('uranus.plays.store');
Route::get('plays/{play}/edit', 'Plays\PlaysController@edit')->name('uranus.plays.edit');
Route::get('plays/{play}/editTranslatable', 'Plays\PlaysController@editTranslatable')->name('uranus.plays.translatable.edit');
Route::put('plays/{play}', 'Plays\PlaysController@update')->name('uranus.plays.update');
Route::put('plays/{play}/updateTranslatable', 'Plays\PlaysController@updateTranslatable')->name('uranus.plays.translatable.update');
Route::get('plays/{play}/editParentId', 'Plays\PlaysController@editParentId')->name('uranus.plays.parent.edit');
Route::put('plays/{play}/updateParentId', 'Plays\PlaysController@updateParentId')->name('uranus.plays.parent.update');
Route::put('plays/{play}/removeParentId', 'Plays\PlaysController@removeParentId')->name('uranus.plays.parent.remove');
Route::delete('plays/{play}', 'Plays\PlaysController@destroy')->name('uranus.plays.destroy');

Route::get('plays/{id}/replicate', 'Plays\PlaysController@createReplica')->name('uranus.plays.create.replica');
Route::post('plays/replicate', 'Plays\PlaysController@replicate')->name('uranus.plays.replicate');
Route::get('plays/unfinalised', 'Plays\PlaysController@indexUnfinalised')->name('uranus.plays.unfinalised.index');
Route::get('plays/user-saved', 'Plays\PlaysController@indexUserSaved')->name('uranus.plays.user_saved.index');
Route::get('plays/unpublished', 'Plays\PlaysController@indexUnpublished')->name('uranus.plays.unpublished.index');
Route::post('plays/published/update', 'Plays\PlaysController@updatePublished')->name('uranus.plays.published.update');
Route::get('plays/featured', 'Plays\PlaysController@indexFeatured')->name('uranus.plays.featured.index');
Route::post('plays/featured/update', 'Plays\PlaysController@updateFeatured')->name('uranus.plays.featured.update');
Route::get('plays/handpicked', 'Plays\PlaysController@indexHandpicked')->name('uranus.plays.handpicked.index');
Route::post('plays/handpicked/update', 'Plays\PlaysController@updateHandpicked')->name('uranus.plays.handpicked.update');
Route::get('plays/repeated', 'Plays\PlaysController@indexRepeated')->name('uranus.plays.repeated.index');
Route::post('plays/popoverInfo', 'Plays\PlaysController@popoverInfo')->name('uranus.plays.popoverInfo');
Route::get('plays/{id}/timetable', 'Plays\PlayTimetableController@show')->name('uranus.play.timetable.show');
Route::get('plays/{id}/characters', 'Plays\CharactersController@edit')->name('uranus.plays.characters.edit');
Route::put('plays/characters/{id}', 'Plays\CharactersController@update')->name('uranus.plays.characters.update');
Route::post('plays/timetable/create', 'Plays\PlayTimetableController@create')->name('uranus.play.timetable.create');
Route::post('plays/timetable/store', 'Plays\PlayTimetableController@store')->name('uranus.play.timetable.store');

Route::get('/plays/{play}/videos', 'PlaysAnalyticsController@videos')->name('uranus.plays.videos.index');

Route::post('plays/translate', 'Plays\TranslationsController@suggest')->name('uranus.plays.translation.suggest');

// =======================================================================//
//                              Contributions
// =======================================================================//
Route::get('contributions', 'ContributionsController@index')->name('uranus.contributions.index');
Route::get('contributions/{contribution}', 'ContributionsController@show')->name('uranus.contributions.show');
Route::get('contributions/download/{contribution}', 'ContributionsController@download')->name('uranus.contributions.download');
Route::put('contributions/{contribution}', 'ContributionsController@update')->name('uranus.contributions.update');
Route::delete('contributions/{contribution}', 'ContributionsController@destroy')->name('uranus.contributions.destroy');
Route::delete('contributions/download/{contribution}', 'ContributionsController@destroyDownload')->name('uranus.contributions.download.destroy');

// =======================================================================//
//                              Contact
// =======================================================================//
Route::get('contacts', 'ContactsController@index')->name('uranus.contacts.index');
Route::get('collaborations', 'ContactsController@indexCollaborations')->name('uranus.collaborations.index');
Route::get('contacts/{contact}', 'ContactsController@show')->name('uranus.contacts.show');
Route::put('contacts/{contact}', 'ContactsController@update')->name('uranus.contacts.update');
Route::delete('contacts/{contact}', 'ContactsController@destroy')->name('uranus.contacts.destroy');


// =======================================================================//
//                      Email Messages & Actions
// =======================================================================//
Route::get('email-messages', 'EmailMessagesController@index')->name('uranus.emailMessages.index');
Route::get('email-messages/{message}', 'EmailMessagesController@show')->name('uranus.emailMessages.show');
Route::get('email-message-actions', 'EmailMessageActionsController@index')->name('uranus.emailMessageActions.index');
Route::get('email-message-actions/{action}', 'EmailMessageActionsController@show')->name('uranus.emailMessageActions.show');


// =======================================================================//
//                               Roles
// =======================================================================//

Route::get('roles', 'RolesController@index')->name('uranus.roles.index');
Route::get('roles/create', 'RolesController@create')->name('uranus.roles.create');
Route::post('roles', 'RolesController@store')->name('uranus.roles.store');
Route::get('roles/{role}/edit', 'RolesController@edit')->name('uranus.roles.edit');
Route::put('roles/{role}', 'RolesController@update')->name('uranus.roles.update');
Route::delete('roles/{role}', 'RolesController@destroy')->name('uranus.roles.destroy');

// =======================================================================//
//                               Reviews (all types)
// =======================================================================//
Route::get('reviews', 'ReviewsController@index')->name('uranus.reviews.index');
Route::post('reviews/{review}', 'ReviewsController@update')->name('uranus.reviews.update');
Route::delete('reviews/{review}', 'ReviewsController@destroy')->name('uranus.reviews.destroy');

Route::get('movieReviews', 'MovieReviewsController@index')->name('uranus.movieReviews.index');
Route::post('movieReviews/{review}', 'MovieReviewsController@update')->name('uranus.movieReviews.update');
Route::delete('movieReviews/{review}', 'MovieReviewsController@destroy')->name('uranus.movieReviews.destroy');

Route::get('tvShowReviews', 'TvShowReviewsController@index')->name('uranus.tvShowReviews.index');
Route::post('tvShowReviews/{review}', 'TvShowReviewsController@update')->name('uranus.tvShowReviews.update');
Route::delete('tvShowReviews/{review}', 'TvShowReviewsController@destroy')->name('uranus.tvShowReviews.destroy');

// =======================================================================//
//                        Genres / Supergenres / Film Genres
// =======================================================================//
Route::get('genres', 'GenresController@index')->name('uranus.genres.index');
Route::get('genres/create', 'GenresController@create')->name('uranus.genres.create');
Route::post('genres', 'GenresController@store')->name('uranus.genres.store');
Route::get('genres/{genre}/edit', 'GenresController@edit')->name('uranus.genres.edit');
Route::put('genres/{genre}', 'GenresController@update')->name('uranus.genres.update');
Route::delete('genres/{genre}', 'GenresController@destroy')->name('uranus.genres.destroy');
Route::get('supergenres', 'SupergenresController@index')->name('uranus.supergenres.index');
Route::get('supergenres/create', 'SupergenresController@create')->name('uranus.supergenres.create');
Route::post('supergenres', 'SupergenresController@store')->name('uranus.supergenres.store');
Route::get('supergenres/{supergenre}/edit', 'SupergenresController@edit')->name('uranus.supergenres.edit');
Route::put('supergenres/{supergenre}', 'SupergenresController@update')->name('uranus.supergenres.update');
Route::delete('supergenres/{supergenre}', 'SupergenresController@destroy')->name('uranus.supergenres.destroy');
Route::get('supergenres/{supergenre}/editGenres', 'SupergenresController@editGenres')->name('uranus.supergenres.editGenres');
Route::put('supergenres/{supergenre}/updateGenres', 'SupergenresController@updateGenres')->name('uranus.supergenres.updateGenres');

Route::get('filmGenres', 'FilmGenresController@index')->name('uranus.filmGenres.index');
Route::get('filmGenres/create', 'FilmGenresController@create')->name('uranus.filmGenres.create');
Route::post('filmGenres', 'FilmGenresController@store')->name('uranus.filmGenres.store');
Route::get('filmGenres/{genre}/edit', 'FilmGenresController@edit')->name('uranus.filmGenres.edit');
Route::put('filmGenres/{genre}', 'FilmGenresController@update')->name('uranus.filmGenres.update');
Route::delete('filmGenres/{genre}', 'FilmGenresController@destroy')->name('uranus.filmGenres.destroy');

// =======================================================================//
//                        Tv Channels
// =======================================================================//
Route::get('tvChannels', 'TvChannelsController@index')->name('uranus.tvChannels.index');
Route::get('tvChannels/create', 'TvChannelsController@create')->name('uranus.tvChannels.create');
Route::post('tvChannels', 'TvChannelsController@store')->name('uranus.tvChannels.store');
Route::get('tvChannels/{tvChannel}/edit', 'TvChannelsController@edit')->name('uranus.tvChannels.edit');
Route::put('tvChannels/{tvChannel}', 'TvChannelsController@update')->name('uranus.tvChannels.update');
Route::delete('tvChannels/{tvChannel}', 'TvChannelsController@destroy')->name('uranus.tvChannels.destroy');

// =======================================================================//
//                        Schools / Alumni
// =======================================================================//
Route::get('schools', 'SchoolsController@index')->name('uranus.schools.index');
Route::get('schools/create', 'SchoolsController@create')->name('uranus.schools.create');
Route::post('schools', 'SchoolsController@store')->name('uranus.schools.store');
Route::get('schools/{school}/edit', 'SchoolsController@edit')->name('uranus.schools.edit');
Route::get('schools/{school}/editTranslatable', 'SchoolsController@editTranslatable')->name('uranus.schools.translatable.edit');
Route::put('schools/{school}', 'SchoolsController@update')->name('uranus.schools.update');
Route::put('schools/{school}/updateTranslatable', 'SchoolsController@updateTranslatable')->name('uranus.schools.translatable.update');
Route::delete('schools/{school}', 'SchoolsController@destroy')->name('uranus.schools.destroy');

Route::post('schools/translate', 'SchoolTranslationsController@suggest')->name('uranus.schools.translation.suggest');

Route::get('schools/{school}/alumni/edit', 'SchoolsController@alumniEdit')->name('uranus.schools.alumni.edit');

Route::post('schoolPerson/store', 'SchoolsController@addPerson')->name('uranus.schoolPerson.store');
Route::post('schoolPerson/destroy', 'SchoolsController@removePerson')->name('uranus.schoolPerson.destroy');

Route::get('alumni/{alumnus}/edit', 'AlumniController@edit')->name('uranus.alumni.edit');
Route::put('alumni/{alumnus}', 'AlumniController@update')->name('uranus.alumni.update');
Route::delete('alumni/{alumnus}', 'AlumniController@destroy')->name('uranus.alumni.destroy');

// =======================================================================//
//                        Awards
// =======================================================================//
Route::get('awards', 'AwardsController@index')->name('uranus.awards.index');
Route::get('awards/create', 'AwardsController@create')->name('uranus.awards.create');
Route::post('awards', 'AwardsController@store')->name('uranus.awards.store');
Route::get('awards/{award}/edit', 'AwardsController@edit')->name('uranus.awards.edit');
Route::put('awards/{award}', 'AwardsController@update')->name('uranus.awards.update');
Route::delete('awards/{award}', 'AwardsController@destroy')->name('uranus.awards.destroy');

Route::get('awards/{award}/awardings/edit', 'AwardsController@awardingsEdit')->name('uranus.awards.awardings.edit');

Route::post('awardPerson/store', 'AwardsController@addPerson')->name('uranus.awardPerson.store');
Route::post('awardPerson/destroy', 'AwardsController@removePerson')->name('uranus.awardPerson.destroy');

Route::get('awardings/{awardPerson}/edit', 'AwardingsController@edit')->name('uranus.awardings.edit');
Route::put('awardings/{awardPerson}', 'AwardingsController@update')->name('uranus.awardings.update');
Route::delete('awardings/{awarding}', 'AwardingsController@destroy')->name('uranus.awardings.destroy');

// =======================================================================//
//                        Albums
// =======================================================================//
Route::get('albums', 'AlbumsController@index')->name('uranus.albums.index');
Route::get('albums/create', 'AlbumsController@create')->name('uranus.albums.create');
Route::post('albums', 'AlbumsController@store')->name('uranus.albums.store');
Route::get('albums/{album}/edit', 'AlbumsController@edit')->name('uranus.albums.edit');
Route::put('albums/{album}', 'AlbumsController@update')->name('uranus.albums.update');
Route::delete('albums/{album}', 'AlbumsController@destroy')->name('uranus.albums.destroy');

// =======================================================================//
//                        Books
// =======================================================================//
Route::get('books', 'BooksController@index')->name('uranus.books.index');
Route::get('books/create', 'BooksController@create')->name('uranus.books.create');
Route::post('books', 'BooksController@store')->name('uranus.books.store');
Route::get('books/{book}/edit', 'BooksController@edit')->name('uranus.books.edit');
Route::put('books/{book}', 'BooksController@update')->name('uranus.books.update');
Route::delete('books/{book}', 'BooksController@destroy')->name('uranus.books.destroy');

// =======================================================================//
//                        Seminars
// =======================================================================//
Route::get('seminars', 'SeminarsController@indexUserSaved')->name('uranus.seminars.user_saved.index');
Route::get('seminars/{id}/edit', 'SeminarsController@edit')->name('uranus.seminars.edit');
Route::put('seminars/{id}', 'SeminarsController@update')->name('uranus.seminars.update');
Route::delete('seminars/{id}', 'SeminarsController@destroy')->name('uranus.seminars.destroy');

// =======================================================================//
//                               Images
// =======================================================================//
Route::get('images', 'ImagesController@index')->name('uranus.images.index');
Route::get('images-moderation', 'ImagesController@indexForModeration')->name('uranus.images.moderation.index');
Route::post('images-moderation/{image}', 'ImagesController@updateForModeration')->name('uranus.images.moderation.update');
Route::get('images-inspection', 'ImagesController@indexForInspection')->name('uranus.images.inspection.index');
Route::post('images-ban/{image}', 'ImagesController@updateForBan')->name('uranus.images.ban.update');
Route::put('images/{image}', 'ImagesController@update')->name('uranus.images.update');
Route::delete('images', 'ImagesController@destroy')->name('uranus.images.destroy');

Route::post('images/update', 'ImagesController@update')->name('uranus.images.update');
Route::post('images/tag', 'ImagesController@tag')->name('uranus.images.tag');
Route::post('images/main', 'ImagesController@mainImage')->name('uranus.images.main');

Route::post('imagePerson/store', 'ImagesController@addPerson')->name('uranus.imagePerson.store');
Route::post('imagePerson/destroy', 'ImagesController@removePerson')->name('uranus.imagePerson.destroy');

// =======================================================================//
//                               Articles
// =======================================================================//
Route::get('interviews', 'ArticlesController@interviewsIndex')->name('uranus.interviews.index');
Route::get('myinterviews', 'ArticlesController@myInterviewsIndex')->name('uranus.myInterviews.index');
Route::get('critiques', 'ArticlesController@critiquesIndex')->name('uranus.critiques.index');
Route::get('mycritiques', 'ArticlesController@myCritiquesIndex')->name('uranus.myCritiques.index');
Route::get('userposts', 'ArticlesController@userpostsIndex')->name('uranus.userposts.index');
Route::get('newsposts', 'ArticlesController@newspostsIndex')->name('uranus.newsposts.index');
Route::get('mynewsposts', 'ArticlesController@myNewspostsIndex')->name('uranus.myNewsposts.index');
Route::get('externals', 'ArticlesController@externalsIndex')->name('uranus.externals.index');
Route::get('interviews/create', 'ArticlesController@interviewCreate')->name('uranus.interviews.create');
Route::get('critiques/create', 'ArticlesController@critiqueCreate')->name('uranus.critiques.create');
Route::get('userposts/create', 'ArticlesController@userpostCreate')->name('uranus.userposts.create');
Route::get('newsposts/create', 'ArticlesController@newspostCreate')->name('uranus.newsposts.create');
Route::get('externals/create', 'ArticlesController@externalCreate')->name('uranus.externals.create');
Route::post('articles', 'ArticlesController@store')->name('uranus.articles.store');
Route::get('articles/{article}/edit', 'ArticlesController@articleEdit')->name('uranus.articles.edit');
Route::put('articles/{article}', 'ArticlesController@update')->name('uranus.articles.update');
Route::delete('articles/{article}', 'ArticlesController@destroy')->name('uranus.articles.destroy');

Route::post('articlePerson/store', 'ArticlesController@addPerson')->name('uranus.articlePerson.store');
Route::post('articlePerson/destroy', 'ArticlesController@removePerson')->name('uranus.articlePerson.destroy');
Route::post('articlePlay/store', 'ArticlesController@addPlay')->name('uranus.articlePlay.store');
Route::post('articlePlay/destroy', 'ArticlesController@removePlay')->name('uranus.articlePlay.destroy');
Route::post('articleMovie/store', 'ArticlesController@addMovie')->name('uranus.articleMovie.store');
Route::post('articleMovie/destroy', 'ArticlesController@removeMovie')->name('uranus.articleMovie.destroy');
Route::post('articleEndeavour/store', 'ArticlesController@addEndeavour')->name('uranus.articleEndeavour.store');
Route::post('articleEndeavour/destroy', 'ArticlesController@removeEndeavour')->name('uranus.articleEndeavour.destroy');
Route::post('articleTvShow/store', 'ArticlesController@addTvShow')->name('uranus.articleTvShow.store');
Route::post('articleTvShow/destroy', 'ArticlesController@removeTvShow')->name('uranus.articleTvShow.destroy');
Route::post('articleUserAuthor/store', 'ArticlesController@addUserAuthor')->name('uranus.articleUserAuthor.store');
Route::post('articleUserAuthor/destroy', 'ArticlesController@removeUserAuthor')->name('uranus.articleUserAuthor.destroy');

// =======================================================================//
//                               Authors (of articles)
// =======================================================================//
Route::get('authors', 'AuthorsController@index')->name('uranus.authors.index');
Route::get('authors/create', 'AuthorsController@create')->name('uranus.authors.create');
Route::post('authors', 'AuthorsController@store')->name('uranus.authors.store');
Route::get('authors/{author}/edit', 'AuthorsController@edit')->name('uranus.authors.edit');
Route::put('authors/{author}', 'AuthorsController@update')->name('uranus.authors.update');
Route::delete('authors/{author}', 'AuthorsController@destroy')->name('uranus.authors.destroy');

// =======================================================================//
//                              Tags
// =======================================================================//
Route::get('tags', 'TagsController@index')->name('uranus.tags.index');
Route::get('tags/create', 'TagsController@create')->name('uranus.tags.create');
Route::post('tags', 'TagsController@store')->name('uranus.tags.store');
Route::get('tags/{tag}/edit', 'TagsController@edit')->name('uranus.tags.edit');
Route::put('tags/{tag}', 'TagsController@update')->name('uranus.tags.update');
Route::delete('tags/{tag}', 'TagsController@destroy')->name('uranus.tags.destroy');

// =======================================================================//
//                            Admins / Users
// =======================================================================//
Route::get('admins', 'AdminsController@index')->name('uranus.admins.index');
Route::get('admins/create', 'AdminsController@create')->name('uranus.admins.create');
Route::post('admins', 'AdminsController@store')->name('uranus.admins.store');
Route::get('admins/{id}/edit', 'AdminsController@edit')->name('uranus.admins.edit');
Route::put('admins/{id}', 'AdminsController@update')->name('uranus.admins.update');
Route::delete('admins/{id}', 'AdminsController@destroy')->name('uranus.admins.destroy');

Route::post('users/userList', 'UsersController@getUserList')->name('uranus.users.userList');
Route::get('users', 'UsersController@index')->name('uranus.users.index');
Route::get('users/create', 'UsersController@create')->name('uranus.users.create');
Route::get('users/{id}/edit', 'UsersController@edit')->name('uranus.users.edit');
Route::get('users/{id}/editEmail', 'UsersController@editEmail')->name('uranus.users.editEmail');
Route::get('users/{id}/editSlug', 'UsersController@editSlug')->name('uranus.users.editSlug');
Route::put('users/{user}/emailUpdate', 'UsersController@updateEmail')->name('uranus.users.updateEmail');
Route::put('users/{user}/slugify', 'UsersController@slugify')->name('uranus.users.slugify');
Route::put('users/{user}', 'UsersController@update')->name('uranus.users.update');
Route::delete('users/{user}', 'UsersController@destroy')->name('uranus.users.destroy');
Route::get('users/{id}/supergenres', 'UsersController@followedSupergenres')->name('uranus.users.followedSupergenres');
Route::get('users/{id}/theatres', 'UsersController@followedTheatres')->name('uranus.users.followedTheatres');
Route::get('users/{id}/people', 'UsersController@followedPeople')->name('uranus.users.followedPeople');
Route::get('users/{id}/watchlist', 'UsersController@watchlistedPlays')->name('uranus.users.watchlistedPlays');
Route::get('users/{id}/ratings', 'UsersController@ratedPlays')->name('uranus.users.ratedPlays');
Route::get('users/{id}/reviews', 'UsersController@reviewedPlays')->name('uranus.users.reviewedPlays');
Route::get('users/{id}/playEvents/visits', 'UsersController@playVisitEvents')->name('uranus.users.playVisitEvents');
Route::get('users/{id}/playEvents/ratings', 'UsersController@playRatingEvents')->name('uranus.users.playRatingEvents');
Route::get('users/{id}/playEvents/watchlist', 'UsersController@playWatchlistEvents')->name('uranus.users.playWatchlistEvents');
Route::get('users/{id}/personEvents/visits', 'UsersController@personVisitEvents')->name('uranus.users.personVisitEvents');
Route::get('users/{id}', 'UsersController@show')->name('uranus.users.show');

Route::get('users/{id}/proPeople', 'UsersController@editProPeople')->name('uranus.users.proPeople');

Route::post('users/{id}/sendProWelcomeEmail', 'UserNotificationsController@sendProWelcome')->name('uranus.users.proWelcome.send');


// =======================================================================//
//                               ProPeopleUsers
// =======================================================================//
Route::post('proPersonUser/store', 'ProPeopleUsersController@store')->name('uranus.proPersonUser.store');
Route::delete('proPersonUser/destroy/{user_id}/{person_id}', 'ProPeopleUsersController@destroy')->name('uranus.proPersonUser.destroy');


// =======================================================================//
//                               Access Levels
// =======================================================================//
Route::get('accessLevels', 'AccessLevelsController@index')->name('uranus.accessLevels.index');
Route::get('accessLevels/create', 'AccessLevelsController@create')->name('uranus.accessLevels.create');
Route::post('accessLevels', 'AccessLevelsController@store')->name('uranus.accessLevels.store');
Route::get('accessLevels/{accessLevel}/edit', 'AccessLevelsController@edit')->name('uranus.accessLevels.edit');
Route::put('accessLevels/{accessLevel}', 'AccessLevelsController@update')->name('uranus.accessLevels.update');
Route::delete('accessLevels/{accessLevel}', 'AccessLevelsController@destroy')->name('uranus.accessLevels.destroy');

// =======================================================================//
//                               Access Levels
// =======================================================================//
Route::get('activityLogs', 'ActivityLogsController@index')->name('uranus.activityLogs.index');
Route::delete('activityLogs/{activityLog}', 'ActivityLogsController@destroy')->name('uranus.activityLogs.destroy');

// =======================================================================//
//                               Revisions
// =======================================================================//
Route::get('play/{id}/revisions', 'RevisionsController@indexPlay')->name('uranus.play.revisions');
Route::get('movie/{id}/revisions', 'RevisionsController@indexMovie')->name('uranus.movie.revisions');
Route::get('tvShow/{id}/revisions', 'RevisionsController@indexTvShow')->name('uranus.tvShow.revisions');
Route::get('person/{id}/revisions', 'RevisionsController@indexPerson')->name('uranus.person.revisions');
Route::get('theatre/{id}/revisions', 'RevisionsController@indexTheatre')->name('uranus.theatre.revisions');
Route::get('festival/{id}/revisions', 'RevisionsController@indexFestival')->name('uranus.festival.revisions');

// =======================================================================//
//                           User behaviour numbers
// =======================================================================//
Route::get('followed/people', 'FollowedController@people')->name('uranus.followed.people');
Route::get('followed/theatres', 'FollowedController@theatres')->name('uranus.followed.theatres');
Route::get('followed/supergenres', 'FollowedController@supergenres')->name('uranus.followed.supergenres');
Route::get('rated/plays', 'RatedController@plays')->name('uranus.rated.plays');
Route::get('watchlisted/plays', 'WatchlistedController@plays')->name('uranus.watchlisted.plays');

// =======================================================================//
//                           Analytics
// =======================================================================//
Route::get('analytics/users', 'AnalyticsController@users')->name('uranus.analytics.users');
Route::get('analytics/people', 'AnalyticsController@people')->name('uranus.analytics.people');
Route::get('analytics/plays', 'AnalyticsController@plays')->name('uranus.analytics.plays');
Route::get('analytics/movies', 'AnalyticsController@movies')->name('uranus.analytics.movies');
Route::get('analytics/tvShows', 'AnalyticsController@tvShows')->name('uranus.analytics.tvShows');
Route::get('analytics/people/recent/{days}', 'AnalyticsController@peopleRecentDays')->name('uranus.analytics.people.recent.days');
Route::get('analytics/plays/recent/{days}', 'AnalyticsController@playsRecentDays')->name('uranus.analytics.plays.recent.days');
Route::get('analytics/movies/recent/{days}', 'AnalyticsController@moviesRecentDays')->name('uranus.analytics.movies.recent.days');
Route::get('analytics/tvShows/recent/{days}', 'AnalyticsController@tvShowsRecentDays')->name('uranus.analytics.tvShows.recent.days');
Route::get('analytics/behaviour/actions/recent/{days}', 'AnalyticsController@behaviourActionsRecent')->name('uranus.analytics.behaviour.actions.recent');
Route::get('analytics/behaviour/actions', 'AnalyticsController@behaviourActions')->name('uranus.analytics.behaviour.actions');
Route::get('analytics/behaviour/ratings/{days}', 'AnalyticsController@behaviourRatings')->name('uranus.analytics.behaviour.ratings');
Route::get('analytics/behaviour/users', 'AnalyticsController@behaviourUsers')->name('uranus.analytics.behaviour.users');

// =======================================================================//
//                               Quality
// =======================================================================//
Route::get('quality', 'Quality\QualityController@index')->name('uranus.quality.index');

Route::get('quality/plays/without-theatre', 'Quality\EvaluatePlaysController@withoutTheatre')
     ->name('uranus.quality.plays.withoutTheatre');

Route::get('quality/plays/without-main-image', 'Quality\EvaluatePlaysController@withoutMainImage')
     ->name('uranus.quality.plays.withoutMainImage');

Route::get('quality/plays/without-images', 'Quality\EvaluatePlaysController@withoutImages')
     ->name('uranus.quality.plays.withoutImages');

Route::get('quality/plays/without-people', 'Quality\EvaluatePlaysController@withoutPeople')
     ->name('uranus.quality.plays.withoutPeople');

Route::get('quality/plays/without-genres', 'Quality\EvaluatePlaysController@withoutGenres')
     ->name('uranus.quality.plays.withoutGenres');

Route::get('quality/people/with-bio', 'Quality\EvaluatePeopleController@withBio')
     ->name('uranus.quality.people.withBio');

Route::get('quality/people/without-main-image', 'Quality\EvaluatePeopleController@withoutMainImage')
     ->name('uranus.quality.people.withoutMainImage');

Route::get('quality/people/without-images', 'Quality\EvaluatePeopleController@withoutImages')
     ->name('uranus.quality.people.withoutImages');

Route::get('quality/people/without-plays', 'Quality\EvaluatePeopleController@withoutPlays')
     ->name('uranus.quality.people.withoutPlays');

// =======================================================================//
//                               Movies
// =======================================================================//
Route::post('movies/movieList', 'MoviesController@getMovieList')->name('uranus.movies.movieList');

Route::get('movies', 'MoviesController@index')->name('uranus.movies.index');
Route::get('movies/unfinalised', 'MoviesController@indexUnfinalised')->name('uranus.movies.unfinalised.index');
Route::get('movies/user-saved', 'MoviesController@indexUserSaved')->name('uranus.movies.user_saved.index');
Route::get('movies/create', 'MoviesController@create')->name('uranus.movies.create');
Route::post('movies', 'MoviesController@store')->name('uranus.movies.store');
Route::get('movies/{movie}/edit', 'MoviesController@edit')->name('uranus.movies.edit');
Route::get('movies/{movie}/editTranslatable', 'MoviesController@editTranslatable')->name('uranus.movies.translatable.edit');
Route::put('movies/{movie}', 'MoviesController@update')->name('uranus.movies.update');
Route::put('movies/{movie}/updateTranslatable', 'MoviesController@updateTranslatable')->name('uranus.movies.translatable.update');
Route::delete('movies/{movie}', 'MoviesController@destroy')->name('uranus.movies.destroy');

Route::post('movies/translate', 'MovieTranslationsController@suggest')->name('uranus.movies.translation.suggest');

// =======================================================================//
//                               Tv Shows
// =======================================================================//
Route::post('tvShows/tvShowList', 'TvShowsController@getTvShowList')->name('uranus.tvShows.tvShowList');

Route::get('tvShows', 'TvShowsController@index')->name('uranus.tvShows.index');
Route::get('tvShows/unfinalised', 'TvShowsController@indexUnfinalised')->name('uranus.tvShows.unfinalised.index');
Route::get('tvShows/user-saved', 'TvShowsController@indexUserSaved')->name('uranus.tvShows.user_saved.index');
Route::get('tvShows/create', 'TvShowsController@create')->name('uranus.tvShows.create');
Route::post('tvShows', 'TvShowsController@store')->name('uranus.tvShows.store');
Route::get('tvShows/{tvShow}/edit', 'TvShowsController@edit')->name('uranus.tvShows.edit');
Route::get('tvShows/{tvShow}/editTranslatable', 'TvShowsController@editTranslatable')->name('uranus.tvShows.translatable.edit');
Route::put('tvShows/{tvShow}', 'TvShowsController@update')->name('uranus.tvShows.update');
Route::put('tvShows/{tvShow}/updateTranslatable', 'TvShowsController@updateTranslatable')->name('uranus.tvShows.translatable.update');
Route::delete('tvShows/{tvShow}', 'TvShowsController@destroy')->name('uranus.tvShows.destroy');

Route::post('tvShows/translate', 'TvShowTranslationsController@suggest')->name('uranus.tvShows.translation.suggest');

// =======================================================================//
//                               Videos
// =======================================================================//
Route::get('videos', 'VideosController@index')->name('uranus.videos.index');
Route::get('videos/user-saved', 'VideosController@indexUserSaved')->name('uranus.videos.user_saved.index');
Route::get('videos/create', 'VideosController@create')->name('uranus.videos.create');
Route::post('videos', 'VideosController@store')->name('uranus.videos.store');
Route::get('videos/{video}/edit', 'VideosController@edit')->name('uranus.videos.edit');
Route::put('videos/{video}', 'VideosController@update')->name('uranus.videos.update');
Route::delete('videos/{video}', 'VideosController@destroy')->name('uranus.videos.destroy');

Route::post('videoPlay/store', 'VideosController@addPlay')->name('uranus.videoPlay.store');
Route::post('videoPlay/destroy', 'VideosController@removePlay')->name('uranus.videoPlay.destroy');
Route::post('videoMovie/store', 'VideosController@addMovie')->name('uranus.videoMovie.store');
Route::post('videoMovie/destroy', 'VideosController@removeMovie')->name('uranus.videoMovie.destroy');
Route::post('videoTvShow/store', 'VideosController@addTvShow')->name('uranus.videoTvShow.store');
Route::post('videoTvShow/destroy', 'VideosController@removeTvShow')->name('uranus.videoTvShow.destroy');
Route::post('videoPerson/store', 'VideosController@addPerson')->name('uranus.videoPerson.store');
Route::post('videoPerson/destroy', 'VideosController@removePerson')->name('uranus.videoPerson.destroy');

// =======================================================================//
//                               Streamings
// =======================================================================//
Route::get('streamings', 'StreamingsController@index')->name('uranus.streamings.index');
Route::get('streamings/create', 'StreamingsController@create')->name('uranus.streamings.create');
Route::post('streamings', 'StreamingsController@store')->name('uranus.streamings.store');
Route::get('streamings/{streaming}/edit', 'StreamingsController@edit')->name('uranus.streamings.edit');
Route::put('streamings/{streaming}', 'StreamingsController@update')->name('uranus.streamings.update');
Route::delete('streamings/{streaming}', 'StreamingsController@destroy')->name('uranus.streamings.destroy');

Route::post('streamingPlay/store', 'StreamingsController@addPlay')->name('uranus.streamingPlay.store');
Route::post('streamingPlay/destroy', 'StreamingsController@removePlay')->name('uranus.streamingPlay.destroy');
Route::post('streamingMovie/store', 'StreamingsController@addMovie')->name('uranus.streamingMovie.store');
Route::post('streamingMovie/destroy', 'StreamingsController@removeMovie')->name('uranus.streamingMovie.destroy');

// =======================================================================//
//                               Slug Redirects
// =======================================================================//
Route::get('slug-redirects', 'SlugRedirectsController@index')->name('uranus.slugRedirects.index');
Route::get('slug-redirects/create', 'SlugRedirectsController@create')->name('uranus.slugRedirects.create');
Route::post('slug-redirects', 'SlugRedirectsController@store')->name('uranus.slugRedirects.store');
Route::get('slug-redirects/{id}/edit', 'SlugRedirectsController@edit')->name('uranus.slugRedirects.edit');
Route::put('slug-redirects/{id}', 'SlugRedirectsController@update')->name('uranus.slugRedirects.update');
Route::delete('slug-redirects/{streaming}', 'SlugRedirectsController@destroy')->name('uranus.slugRedirects.destroy');


// =======================================================================//
//                        Subscriptions
// =======================================================================//
Route::get('subscriptions', 'SubscriptionsController@index')->name('uranus.subscriptions.index');
Route::get('subscriptions/create', 'SubscriptionsController@create')->name('uranus.subscriptions.create');
Route::post('subscriptions', 'SubscriptionsController@store')->name('uranus.subscriptions.store');
Route::get('subscriptions/{subscription}/edit', 'SubscriptionsController@edit')->name('uranus.subscriptions.edit');
Route::put('subscriptions/{subscription}', 'SubscriptionsController@update')->name('uranus.subscriptions.update');
Route::delete('subscriptions/{subscription}', 'SubscriptionsController@destroy')->name('uranus.subscriptions.destroy');
Route::post('subscriptionPerson/store', 'SubscriptionsController@addPerson')->name('uranus.subscriptionPerson.store');
Route::post('subscriptionPerson/destroy', 'SubscriptionsController@removePerson')->name('uranus.subscriptionPerson.destroy');

// =======================================================================//
//                        Pro Leads
// =======================================================================//
Route::get('proLeads', 'ProLeadsController@index')->name('uranus.proLeads.index');
Route::get('proLeads/{proLead}/edit', 'ProLeadsController@edit')->name('uranus.proLeads.edit');
Route::put('proLeads/{proLead}', 'ProLeadsController@update')->name('uranus.proLeads.update');
Route::delete('proLeads/{proLead}', 'ProLeadsController@destroy')->name('uranus.proLeads.destroy');

// =======================================================================//
//                        Endeavours
// =======================================================================//
Route::get('endeavours', 'Endeavours\EndeavoursController@index')->name('uranus.endeavours.index');
Route::post('endeavour/endeavourList', 'Endeavours\EndeavoursController@getEndeavourList')->name('uranus.endeavours.endeavourList');
Route::get('endeavours/user-saved', 'Endeavours\EndeavoursController@indexUserSaved')->name('uranus.endeavours.user_saved.index');
Route::get('endeavours/create', 'Endeavours\EndeavoursController@create')->name('uranus.endeavours.create');
Route::post('endeavours/popoverInfo', 'Endeavours\EndeavoursController@popoverInfo')->name('uranus.endeavours.popoverInfo');
Route::post('endeavours', 'Endeavours\EndeavoursController@store')->name('uranus.endeavours.store');
Route::get('endeavours/{endeavour}/edit', 'Endeavours\EndeavoursController@edit')->name('uranus.endeavours.edit');
Route::put('endeavours/{endeavour}', 'Endeavours\EndeavoursController@update')->name('uranus.endeavours.update');
Route::delete('endeavours/{endeavour}', 'Endeavours\EndeavoursController@destroy')->name('uranus.endeavours.destroy');

Route::get('endeavours/{id}/characters', 'Endeavours\CharactersController@edit')->name('uranus.endeavours.characters.edit');
Route::put('endeavours/characters/{id}', 'Endeavours\CharactersController@update')->name('uranus.endeavours.characters.update');

// =======================================================================//
//                        Varieties
// =======================================================================//
Route::get('varieties', 'VarietiesController@index')->name('uranus.varieties.index');
Route::get('varieties/create', 'VarietiesController@create')->name('uranus.varieties.create');
Route::post('varieties', 'VarietiesController@store')->name('uranus.varieties.store');
Route::get('varieties/{variety}/edit', 'VarietiesController@edit')->name('uranus.varieties.edit');
Route::put('varieties/{variety}', 'VarietiesController@update')->name('uranus.varieties.update');
Route::delete('varieties/{variety}', 'VarietiesController@destroy')->name('uranus.varieties.destroy');

Route::get('varieties/{variety}/roles', 'VarietiesController@editRoles')->name('uranus.varieties.roles.edit');
Route::put('varieties/roles/{id}', 'VarietiesController@updateRoles')->name('uranus.varieties.roles.update');

// =======================================================================//
//                  Digital Platform Urls
// =======================================================================//
Route::get('digital-platform-urls/create', 'DigitalPlatformUrlsController@create')->name('uranus.digital_platform_urls.create');
Route::post('digital-platform-urls', 'DigitalPlatformUrlsController@store')->name('uranus.digital_platform_urls.store');
Route::get('digital-platform-urls/{digital_platform_url}/edit', 'DigitalPlatformUrlsController@edit')->name('uranus.digital_platform_urls.edit');
Route::put('digital-platform-urls/{digital_platform_url}', 'DigitalPlatformUrlsController@update')->name('uranus.digital_platform_urls.update');
Route::delete('digital-platform-urls/{digital_platform_url}', 'DigitalPlatformUrlsController@destroy')->name('uranus.digital_platform_urls.destroy');

// =======================================================================//
//                        Log viewer
// =======================================================================//
Route::prefix('logs')
    ->namespace('\Arcanedev\LogViewer\Http\Controllers')
    ->group(function () {
        Route::get('/', 'LogViewerController@index')
            ->name('log-viewer::dashboard');
        Route::prefix('logs')
            ->name('log-viewer::logs.')
            ->group(function () {
                Route::get('/', 'LogViewerController@listLogs')
                    ->name('list');
                Route::delete('/delete', 'LogViewerController@delete')
                    ->name('delete');
                Route::get('/{date}', 'LogViewerController@show')
                    ->name('show');
                Route::get('/{date}/download', 'LogViewerController@download')
                    ->name('download');
                Route::get('/{date}/{level}', 'LogViewerController@showByLevel')
                    ->name('filter');
                Route::get('/{date}/{level}/search', 'LogViewerController@search')
                    ->name('search');
            });
    });
