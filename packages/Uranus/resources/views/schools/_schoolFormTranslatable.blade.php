<div class="form-group row">
    <div class="col-sm-12">
        <label for="name" class="control-label">Όνομα σχολής:*</label>
        <input type="text" class="form-control" name="name" id="name" value="{!! $school->name !!}" readonly="readonly">
    </div>
</div>
<div class="form-group row">
    <div class="col-sm-12">
        <a style="cursor:pointer" data-url="{!! route('uranus.schools.translation.suggest') !!}" href="" id="translate_name">Πρότεινε μετάφρασιν</a>
    </div>
</div>
<div class="form-group row">
    <div class="col-sm-12">
        <label for="name_en" class="control-label">Όνομα σχολής [Αγγλικά]:*</label>
        <input type="text" class="form-control" name="name_en" id="name_en" value="{{ old('name_en',($school->hasTranslation('en') ? $school->translate('en')->name : "")) }}">
    </div>
</div>

<div class="form-group row">
    <div class="col-sm-12">
        <label for="description" class="control-label">Περιγραφή:</label>
        <textarea name="description" id="description" class="form-control" rows="4" readonly="readonly">{{ strip_tags($school->description) }}</textarea>
    </div>
</div>
<div class="form-group row">
    <div class="col-sm-12">
        <a style="cursor:pointer" data-url="{!! route('uranus.schools.translation.suggest') !!}" href="" id="translate_description">Πρότεινε μετάφρασιν</a>
    </div>
</div>
<div class="form-group row">
    <div class="col-sm-12">
        <label for="description_en" class="control-label">Περιγραφή [Αγγλικά]:</label>
        <textarea name="description_en" id="description_en" class="form-control" rows="4">{{ old('description_en', ($school->hasTranslation('en') ? strip_tags($school->translate('en')->description) : "")) }}</textarea>
    </div>
</div>

<div class="form-group row">
    <div class="col-sm-12 col-md-6 col-md-offset-3">
        <button type="submit" class="btn btn-block btn-success">{{ $submitButtonText }}</button>
    </div>
</div>
