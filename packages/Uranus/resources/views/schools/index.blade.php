@extends('uranus::layout')
@section('content')
    <!-- Page -->
    <div class="page animsition">
        <div class="page-header">
            <h1 class="page-title">Θεατρικά schools</h1>
            <div class="page-header-actions">
                @admincan('manage_theatres')
                <a href="{!! route('uranus.schools.create') !!}">
                    <button type="button" class="btn btn-floating btn-danger">
                        <i class="icon wb-plus" aria-hidden="true"></i></button>
                </a>
                @endadmincan
            </div>
        </div>
        <div class="page-content">
            <!-- Panel Basic -->
            <div class="panel">
                <div class="panel-body">
                    {{--Search bar--}}
                    <div>
                        <form method="get" class="search_form pull-left" action="{{ route('uranus.schools.index') }}">
                            <input type="text" id="search_query" name="q" value="{{ request('q') }}">
                            <button type="submit">Αναζήτηση</button>
                        </form>
                        <span class="pull-right">
                            <a href="{!! route('uranus.schools.index', ["t" => "notTranslated"]) !!}">{!! $notTranslated !!} / {!! $allSchools !!} schools not translated</a>
                        </span>
                    </div>
                    <table class="table table-hover dataTable table-striped width-full" data-plugin=" ">
                        <thead>
                            <tr>
                                <th>{!! \App\Components\LinkTo::sortableRoute('uranus.schools.index','Showable','showable') !!}</th>
                                <th>{!! \App\Components\LinkTo::sortableRoute('uranus.schools.index','Όνομα','name') !!}</th>
                                <th># Αποφοίτων</th>
                                <th></th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($schools as $school)
                                <tr>
                                    <td>
                                    @if($school->showable)
                                        <div class="text-center">
                                            <i class="icon wb-eye green-600" aria-hidden="true" title="Δημοσιευμένη"></i>
                                        </div>
                                    @else
                                        <div class="text-center">
                                            <i class="icon wb-eye-close red-600" aria-hidden="true" title="Αδημοσίευτη"></i>
                                        </div>
                                    @endif
                                    </td>
                                    <td><a target="_blank" href="{{ route('uranus.schools.edit', $school->id) }}">{{ $school->name }}</a></td>
                                    <td><a target="_blank" href="{{ route('uranus.schools.alumni.edit', $school->id) }}">{{ $school->people()->count() }}</a></td>
                                    @admincan('manage_theatres')
                                    <td>
                                        <a href="{!! route('uranus.schools.edit', $school->id) !!}">
                                            <button type="button" class="btn btn-icon btn-default btn-outline">
                                                <i class="icon wb-pencil" aria-hidden="true"></i></button>
                                        </a>
                                    </td>
                                    @endadmincan
                                    @admincan('delete_theatres')
                                        <td>
                                            <a data-url="{!! route('uranus.schools.destroy', $school->id) !!}" href="" class="deleteResource">
                                                <button type="button" class="btn btn-icon btn-danger btn-outline">
                                                    <i class="fa fa-times" aria-hidden="true"></i></button>
                                            </a>
                                        </td>
                                    @endadmincan
                                </tr>
                            @empty
                                <p>Δε βρέθηκαν θεατρικά schools</p>
                            @endforelse
                        </tbody>
                    </table>
                    {!! $schools->render() !!}
                </div>
            </div>
            <!-- End Panel Basic -->
        </div>
    </div>
    <!-- End Page -->
@stop
@section('scripts')
    @parent
    {!! csrf_field() !!}
    <script src="{{ asset('js/admin/jquery.deleteResource.js') }}"></script>
@stop
