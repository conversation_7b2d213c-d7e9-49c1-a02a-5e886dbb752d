@extends('uranus::layout')
@section('content')
        <!-- Page -->
<div class="page animsition">
    <div class="page-header">
        <h1 class="page-title">{{request('q')? 'Αποτελεσματα Αναζήτησης Συντελεστων για \''.request('q').'\'':'Συντελεστές'}}</h1>

        <div class="page-header-actions">
            @admincan('manage_people')
            <a href="{!! route('uranus.people.create') !!}">
                <button type="button" class="btn btn-floating btn-danger">
                    <i class="icon wb-plus" aria-hidden="true"></i></button>
            </a>
            @endadmincan
        </div>
    </div>
    <div class="page-content">
        <!-- Panel Basic -->
        <div class="panel">
            <div class="panel-body">
                {{--Search bar--}}
                <div>
                    <form method="get" action="{{ route('uranus.people.index') }}" class="search_form pull-left">
                        <input type="text" name="q" id="search_query" value="{{ request('q') }}">
                        <button type="submit">Αναζήτηση</button>
                    </form>
                    @if($notTranslated > 0)
                        <span class="pull-right">
                        <a href="{!! route('uranus.people.index', ["t" => "notTranslated"]) !!}">{!! $notTranslated !!} / {!! $allPeople !!} people not translated</a>
                    </span>
                    @endif
                </div>
                <table class="table table-hover dataTable table-striped width-full" data-plugin=" ">
                    <thead>
                        <tr>
                            <th>{!! \App\Components\LinkTo::sortableRoute('uranus.people.index','Ονοματεπώνυμο','last_name') !!}</th>
                            <th class="text-center">Φωτογραφία Προφιλ</th>
                            <th class="text-center">Παραστάσεις</th>
                            <th>Τηλ. Σειρές</th>
                            <th>Ταινίες</th>
                            <th>Endeavours</th>
                            <th>{!! \App\Components\LinkTo::sortableRoute('uranus.people.index','Ημερομηνία καταχώρησης','created_at') !!}</th>
                            <th></th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($people as $person)
                            <tr>
                                <td><a target="_blank" href="{{ route('uranus.people.edit', $person->id) }}">{{ $person->fullName }}</a></td>
                                <td>
                                    @if($person->mainImage)
                                        <div class="text-center">
                                            <i class="icon wb-user green-600" aria-hidden="true" title="Με φωτογραφία προφιλ"></i>
                                        </div>
                                    @else
                                        <div class="text-center">
                                            <i class="icon wb-user red-600" aria-hidden="true" title="Χωρίς φωτογραφία προφιλ"></i>
                                        </div>
                                    @endif
                                </td>
                                <td class="text-center"><a href="{{ route('uranus.people.castings', $person->id) }}#plays" target="_blank">{{ count($person->plays) }}</a></td>
                                <td class="text-center"><a href="{{ route('uranus.people.castings', $person->id) }}#tvShows" target="_blank">{{ count($person->tvShows) }}</a></td>
                                <td class="text-center"><a href="{{ route('uranus.people.castings', $person->id) }}#movies" target="_blank">{{ count($person->movies) }}</a></td>
                                <td class="text-center"><a href="{{ route('uranus.people.castings', $person->id) }}#endeavours" target="_blank">{{ count($person->endeavours) }}</a></td>
                                <td>{!! $person->created_at !!}</td>
                                @admincan('delete_people')
                                <td>
                                    <a data-url="{!! route('uranus.people.destroy', $person->id) !!}" href="" class="deleteResource">
                                        <button type="button" class="btn btn-icon btn-danger btn-outline">
                                            <i class="fa fa-times" aria-hidden="true"></i></button>
                                    </a>
                                </td>
                                @endadmincan
                            </tr>
                        @empty
                            <p>Δε βρέθηκαν συντελεστές</p>
                        @endforelse
                    </tbody>
                </table>
                {!! $people->render() !!}
            </div>
        </div>
        <!-- End Panel Basic -->
    </div>
</div>
<!-- End Page -->
@stop
@section('scripts')
    @parent
    {!! csrf_field() !!}
    <script src="{{ asset('js/admin/jquery.deleteResource.js') }}"></script>
@stop
