@extends('uranus::layout')
@section('content')
        <!-- Page -->
<div class="page animsition">
    <div class="page-header">
        <h1 class="page-title">{{request('q')? 'Αποτελεσματα Αναζήτησης Θεατρων για \''.request('q').'\'':'Θέατρα'}}</h1>

        <div class="page-header-actions">
            @admincan('manage_theatres')
            <a href="{!! route('uranus.theatres.create') !!}">
                <button type="button" class="btn btn-floating btn-danger">
                    <i class="icon wb-plus" aria-hidden="true"></i></button>
            </a>
            @endadmincan
        </div>
    </div>
    <div class="page-content">
        <!-- Panel Basic -->
        <div class="panel">
            <div class="panel-body">
                {{--Search bar--}}
                <div>
                    <form method="get" class="search_form pull-left" action="{{ route('uranus.theatres.index') }}">
                        <input type="text" id="search_query" name="q" value="{{ request('q') }}">
                        <button type="submit">Αναζήτηση</button>
                    </form>
                    <span class="pull-right">
                        <a href="{!! route('uranus.theatres.index', ["t" => "notTranslated"]) !!}">{!! $notTranslated !!} / {!! $allTheatres !!} theatres not translated</a>
                    </span>
                </div>
                <table class="table table-hover dataTable table-striped width-full" data-plugin="">
                    <thead>
                        <tr>
                            <th>{!! \App\Components\LinkTo::sortableRoute('uranus.theatres.index','Όνομα','name') !!}</th>
                            <th>Παραστασεις</th>
                            <th>Περιγραφή</th>
                            <th>Σταθερό τηλ.</th>
                            <th>Διεύθυνση</th>
                            <th>{!! \App\Components\LinkTo::sortableRoute('uranus.theatres.index','Ημερομηνία καταχώρησης','created_at') !!}</th>
                            <th></th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($theatres as $theatre)
                            <tr>
                                <td>{!! link_to_route('uranus.theatres.edit', $theatre->name, $theatre->id) !!}</td>
                                <td>{!! is_countable($theatre->playsArchive) ? count($theatre->playsArchive) : 0 !!}</td>
                                @if(empty($theatre->description))
                                    <td style="color:#f00; font-weight:bold;">Όχι</td>
                                @else
                                    <td>Ναι</td>
                                @endif
                                <td>{!! $theatre->telephone !!}</td>
                                <td>{!! $theatre->address !!}</td>
                                <td>{!! $theatre->created_at !!}</td>
                                @admincan('manage_theatres')
                                <td>
                                    <a href="{!! route('uranus.theatres.edit', $theatre->id) !!}">
                                        <button type="button" class="btn btn-icon btn-default btn-outline">
                                            <i class="icon wb-pencil" aria-hidden="true"></i></button>
                                    </a>
                                </td>
                                @endadmincan
                                @admincan('delete_theatres')
                                <td>
                                    <a data-url="{!! route('uranus.theatres.destroy', $theatre->id) !!}" href="" class="deleteResource">
                                        <button type="button" class="btn btn-icon btn-danger btn-outline">
                                            <i class="fa fa-times" aria-hidden="true"></i></button>
                                    </a>
                                </td>
                                @endadmincan
                            </tr>
                        @empty
                            <p>Δε βρέθηκαν θέατρα</p>
                        @endforelse
                    </tbody>
                </table>
                {!! $theatres->render() !!}
            </div>
        </div>
        <!-- End Panel Basic -->
    </div>
</div>
<!-- End Page -->
@stop

@section('scripts')
    @parent
    {!! csrf_field() !!}
    <script src="{{ asset('js/admin/jquery.deleteResource.js') }}"></script>
@stop
