@extends('uranus::layout')
@section('head')
    @parent
    <link type="text/css" rel="stylesheet" href="https://ajax.googleapis.com/ajax/libs/jqueryui/1.10.2/themes/smoothness/jquery-ui.min.css" media="screen"/>
    @stop
    @section('content')
            <!-- Page -->
    <div class="page animsition">
        <div class="page-header">
            <h1 class="page-title">{{$theatre->name}}</h1>

            <div class="page-header-actions">
                <a href="{{ route('uranus.theatre.revisions',$theatre->id) }}">
                    <button type="button" class="btn btn-floating btn-default">
                        <i class="fa fa-lg fa-history green-600" style="font-size: 24px"></i></button>
                </a>
                <a href="{!! route('theatres.show', $theatre->slug) !!}">
                    <button type="button" class="btn btn-floating btn-primary">
                        <i class="icon wb-eye" aria-hidden="true"></i></button>
                </a>
                <a href="{!! route('uranus.theatres.translatable.edit', $theatre->id) !!}">
                    <button type="button" class="btn btn-floating btn-warning">
                        <i class="icon wb-globe" aria-hidden="true"></i></button>
                </a>
                <a href="{!! route('uranus.theatres.create') !!}">
                    <button type="button" class="btn btn-floating btn-danger">
                        <i class="icon wb-plus" aria-hidden="true"></i></button>
                </a>
            </div>
        </div>
        <div class="page-content">
            <div class="container-fluid">
                @include('uranus::messages.successfulSave')
                @include('uranus::errors.genericForm')
                <p class="text-muted" style="margin-bottom: 15px;">
                    <strong>Slug:</strong> <code>{{ $theatre->slug }}</code>
                </p>
                <form action="{{route('uranus.theatres.update', $theatre->id)}}" method="post" enctype="multipart/form-data">
                    {!! csrf_field() !!}
                    {!! method_field('put') !!}
                    @include('uranus::theatres._theatreForm',['submitButtonText'=>'Αποθήκευση', 'imageRoute' => 'uranus.images.update'])
                </form>
            </div>
        </div>
    </div>
    <!-- End Page -->
@stop
@section('footer')
    @parent
    <script type="text/javascript" src="https://ajax.googleapis.com/ajax/libs/jqueryui/1.10.2/jquery-ui.min.js" charset="UTF-8"></script>
@stop
