@extends('uranus::layout')
@section('content')
        <!-- Page -->
<div class="page animsition">
    <div class="page-header">
        <h1 class="page-title">{{request('q')? 'Αποτελεσματα Αναζήτησης σειρών για \''.request('q').'\'':'Σειρές'}}</h1>
        <div class="page-header-actions">
            <a href="{!! route('uranus.tvShows.create') !!}">
                <button type="button" class="btn btn-floating btn-danger">
                    <i class="icon wb-plus" aria-hidden="true"></i></button>
            </a>
        </div>
    </div>
    <div class="page-content">
        <!-- Panel Basic -->
        <div class="panel">
            <div class="panel-body">
                {{--Search bar--}}
                <div>
                    <form method="get" class="search_form pull-left" action="{{ route('uranus.tvShows.index') }}">
                        <input type="text" id="search_query" name="q" value="{{ request('q') }}">
                        <button type="submit">Αναζήτηση</button>
                    </form>
                    <span class="pull-right">
                        <a href="{!! route('uranus.tvShows.index', ["t" => "notTranslated"]) !!}">{!! $notTranslated !!} / {!! $allTvShows !!} tv shows not translated</a>
                    </span>
                </div>

                <table class="table table-hover dataTable table-striped width-full" data-plugin=" ">
                    <thead>
                        <tr>
                            <th>{!! \App\Components\LinkTo::sortableRoute('uranus.tvShows.index','Δημ/νη','published') !!}</th>
                            <th>{!! \App\Components\LinkTo::sortableRoute('uranus.tvShows.index','Mod','moderated') !!}</th>
                            <th>Τίτλος</th>
                            <th>{!! \App\Components\LinkTo::sortableRoute('uranus.tvShows.index','Ετος','year') !!}</th>
                            <th>{!! \App\Components\LinkTo::sortableRoute('uranus.tvShows.index','Χρήστης','user_id') !!}</th>
                            <th>{!! \App\Components\LinkTo::sortableRoute('uranus.tvShows.index','Τελευταία δραστηριότητα','updated_at') !!}</th>
                            <th>{!! \App\Components\LinkTo::sortableRoute('uranus.tvShows.index','Δημιουργία','created_at') !!}</th>
                            <th></th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($tvShows as $tvShow)
                            <tr>
                                <td>
                                    @if($tvShow->published)
                                        <div class="text-center">
                                            <i class="icon wb-eye green-600" aria-hidden="true" title="Δημοσιευμένη"></i>
                                        </div>
                                    @else
                                        <div class="text-center">
                                            <i class="icon wb-eye-close red-600" aria-hidden="true" title="Αδημοσίευτη"></i>
                                        </div>
                                    @endif
                                </td>
                                <td>
                                    @if($tvShow->moderated)
                                        <div class="text-center">
                                            <i class="icon wb-check-circle green-600" aria-hidden="true" title="Moderated"></i>
                                        </div>
                                    @else
                                        <div class="text-center">
                                            <i class="icon wb-alert-circle red-600" aria-hidden="true" title="Unmoderated"></i>
                                        </div>
                                    @endif
                                </td>
                                <td><a target="_blank" href="{{ route('uranus.tvShows.edit', $tvShow->id) }}">{{ $tvShow->title }}</a></td>
                                <td>{{ $tvShow->year }}</td>
                                <td>
                                    @if($tvShow->user()->exists())
                                        <a href="{!! route('uranus.users.edit', $tvShow->user()->first()->id) !!}" target="_blank" title="{!! $tvShow->user()->first()->fullName !!} {!! $tvShow->user()->first()->email !!}">{!! $tvShow->user()->first()->id !!}</a>
                                    @else
                                        -
                                    @endif
                                </td>
                                <td>{!! $tvShow->updated_at !!}</td>
                                <td>{{ $tvShow->created_at }}</td>
                                @admincan('delete_tv')
                                <td>
                                    <a data-url="{!! route('uranus.tvShows.destroy', $tvShow->id) !!}" href="" class="deleteResource">
                                        <button type="button" class="btn btn-icon btn-danger btn-outline" title="Διαγραφή">
                                            <i class="fa fa-times" aria-hidden="true"></i></button>
                                    </a>
                                </td>
                                @endadmincan
                            </tr>
                        @empty
                            <p>Δε βρέθηκαν σειρές</p>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>
        <!-- End Panel Basic -->
        {!! $tvShows->render() !!}
    </div>
</div>
<!-- End Page -->
@stop

@section('scripts')
    @parent
    {!! csrf_field() !!}
    <script src="{{ asset('js/admin/jquery.deleteResource.js') }}"></script>
@stop
