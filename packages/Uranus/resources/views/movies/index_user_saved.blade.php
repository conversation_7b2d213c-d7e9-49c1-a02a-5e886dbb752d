@extends('uranus::layout')
@section('content')
        <!-- Page -->
<div class="page animsition">
    <div class="page-header">
        <h1 class="page-title">User saved ταινίες</h1>

        <div class="page-header-actions">
        </div>
    </div>
    <div class="page-content">
        <!-- Panel Basic -->
        <div class="panel">

            <div class="panel-body">
                <form action="{{ route('uranus.movies.user_saved.index') }}" method="get">
                    <div class="form-group row">
                        <div class="col-sm-12 col-md-6">
                            <label for="q">Τίτλος / σχόλια χρήστου / σχόλια admin</label>
                            <input type="text" id="search_query" name="q" value="{{ request('q') }}">
                        </div>
                        <div class="col-sm-12 col-md-6">
                            <label for="user_id">ID χρήστου καταχωρητού</label>
                            <input type="text" id="user_id" name="user_id" value="{{ request('user_id') }}">
                        </div>
                    </div>
                    <div class="form-group row">
                        <div class="col-sm-12 col-md-6">
                            <label for="moderated">Moderated</label>
                            <select name="moderated" id="moderated" class="form-control">
                                <option disabled selected value>Επίλεξε</option>
                                <option value="all" @if(request('moderated') == 'all') selected @endif>Όλες</option>
                                <option value="moderated" @if(request('moderated') == 'moderated') selected @endif>Moderated</option>
                                <option value="unmoderated" @if(request('moderated') == 'unmoderated') selected @endif>Unmoderated</option>
                            </select>
                        </div>
                        <div class="col-sm-12 col-md-6">
                            <label for="published">Published</label>
                            <select name="published" id="published" class="form-control">
                                <option disabled selected value>Επίλεξε</option>
                                <option value="all" @if(request('published') == 'all') selected @endif>Όλες</option>
                                <option value="published" @if(request('published') == 'published') selected @endif>Published</option>
                                <option value="unpublished" @if(request('published') == 'unpublished') selected @endif>Unpublished</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-group row">
                        <div class="col-sm-12 col-md-4">
                            <button type="submit">Αναζήτηση</button>
                            <a href="{{ route('uranus.movies.user_saved.index') }}">Όλες</a>
                        </div>
                    </div>
                </form>

                <table class="table table-hover dataTable table-striped width-full" data-plugin=" ">
                    <thead>
                        <tr>
                            <th>{!! \App\Components\LinkTo::sortableRoute('uranus.movies.user_saved.index','Δημ','published') !!}</th>
                            <th>{!! \App\Components\LinkTo::sortableRoute('uranus.movies.user_saved.index','Mod','moderated') !!}</th>
                            <th>Τίτλος</th>
                            <th>{!! \App\Components\LinkTo::sortableRoute('uranus.movies.user_saved.index','Έτος','year') !!}</th>
                            <th>{!! \App\Components\LinkTo::sortableRoute('uranus.movies.user_saved.index','Τελευταία δραστηριότητα','updated_at') !!}</th>
                            <th>{!! \App\Components\LinkTo::sortableRoute('uranus.movies.user_saved.index','Χρήστης','user_id') !!}</th>
                            <th>{!! \App\Components\LinkTo::sortableRoute('uranus.movies.user_saved.index','Σημειώσεις admin','notes') !!}</th>
                            <th>{!! \App\Components\LinkTo::sortableRoute('uranus.movies.user_saved.index','Σημειώσεις χρήστου','user_notes') !!}</th>
                            <th></th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($movies as $movie)
                            <tr>
                                <td>
                                @if($movie->published)
                                    <div class="text-center">
                                        <i class="icon wb-eye green-600" aria-hidden="true" title="Δημοσιευμένη"></i>
                                    </div>
                                @else
                                    <div class="text-center">
                                        <i class="icon wb-eye-close red-600" aria-hidden="true" title="Μη δημοσιευμένη"></i>
                                    </div>
                                @endif
                                </td>
                                <td>
                                @if($movie->moderated)
                                    <div class="text-center">
                                        <i class="icon wb-check-circle green-600" aria-hidden="true" title="Moderated"></i>
                                    </div>
                                @else
                                    <div class="text-center">
                                        <i class="icon wb-alert-circle red-600" aria-hidden="true" title="Unmoderated"></i>
                                    </div>
                                @endif
                                </td>
                                <td><a href="{!! route('uranus.movies.edit', $movie->id) !!}" target="_blank">{!! $movie->title !!}</a></td>
                                <td>{!! $movie->year !!}</td>
                                <td>{!! $movie->updated_at !!}</td>
                                <td><a href="{!! route('uranus.users.edit', $movie->user->id) !!}" target="_blank" title="{!! $movie->user->fullName !!}, {!! $movie->user->email !!}">{!! $movie->user->id !!}</a></td>
                                <td>{!! $movie->notes !!}</td>
                                <td>{!! $movie->user_notes !!}</td>
                                @admincan('delete_plays')
                                <td>
                                    <a data-url="{!! route('uranus.movies.destroy', $movie->id) !!}" href="" class="deleteResource">
                                        <button type="button" class="btn btn-icon btn-danger btn-outline" title="Διαγραφή">
                                            <i class="fa fa-times" aria-hidden="true"></i></button>
                                    </a>
                                </td>
                                @endadmincan
                            </tr>
                        @empty
                            <p>Δε βρέθηκαν ταινίες</p>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>
        <!-- End Panel Basic -->
        {!! $movies->render() !!}
    </div>
</div>
<!-- End Page -->
{!! csrf_field() !!}

@stop
@section('scripts')
    @parent
    {!! csrf_field() !!}
    <script src="{{ asset('js/admin/jquery.deleteResource.js') }}"></script>
@stop
