@extends('uranus::layout')
@section('content')
        <!-- Page -->
<div class="page animsition">
    <div class="page-header">
        <h1 class="page-title">{{request('q')? 'Αποτελεσματα Αναζήτησης ταινιων για \''.request('q').'\'':'Ταινίες'}}</h1>
        <div class="page-header-actions">
            <a href="{!! route('uranus.movies.create') !!}">
                <button type="button" class="btn btn-floating btn-danger">
                    <i class="icon wb-plus" aria-hidden="true"></i></button>
            </a>
        </div>
    </div>
    <div class="page-content">
        <!-- Panel Basic -->
        <div class="panel">
            <div class="panel-body">
                {{--Search bar--}}
                <div>
                    <form method="get" class="search_form pull-left" action="{{ route('uranus.movies.index') }}">
                        <input type="text" id="search_query" name="q" value="{{ request('q') }}">
                        <button type="submit">Αναζήτηση</button>
                    </form>
                    @if($notTranslated > 0)
                        <span class="pull-right">
                        <a href="{!! route('uranus.movies.index', ["t" => "notTranslated"]) !!}">{!! $notTranslated !!} / {!! $allMovies !!} movies not translated</a>
                    </span>
                    @endif
                </div>

                <table class="table table-hover dataTable table-striped width-full" data-plugin=" ">
                    <thead>
                        <tr>
                            <th>{!! \App\Components\LinkTo::sortableRoute('uranus.movies.index','Δημ/νη','published') !!}</th>
                            <th>Τίτλος</th>
                            <th>{!! \App\Components\LinkTo::sortableRoute('uranus.movies.index','Ετος','year') !!}</th>
                            <th>{!! \App\Components\LinkTo::sortableRoute('uranus.movies.index','Δημιουργία','created_at') !!}</th>
                            <th></th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($movies as $movie)
                            <tr>
                                <td>@if($movie->published)
                                        <div class="text-center">
                                            <i class="icon wb-eye green-600" aria-hidden="true" title="Δημοσιευμένη"></i>
                                        </div>
                                    @else
                                        <div class="text-center">
                                            <i class="icon wb-eye-close red-600" aria-hidden="true" title="Αδημοσίευτη"></i>
                                        </div>
                                    @endif
                                </td>
                                <td><a target="_blank" href="{{ route('uranus.movies.edit', $movie->id) }}">{{ $movie->title }}</a></td>
                                <td>{{ $movie->year }}</td>
                                <td>{{ $movie->created_at }}</td>
                                @admincan('delete_movies')
                                <td>
                                    <a data-url="{!! route('uranus.movies.destroy', $movie->id) !!}" href="" class="deleteResource">
                                        <button type="button" class="btn btn-icon btn-danger btn-outline" title="Διαγραφή">
                                            <i class="fa fa-times" aria-hidden="true"></i></button>
                                    </a>
                                </td>
                                @endadmincan
                            </tr>
                        @empty
                            <p>Δε βρέθηκαν ταινίες</p>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>
        <!-- End Panel Basic -->
        {!! $movies->render() !!}
    </div>
</div>
<!-- End Page -->
@stop

@section('scripts')
    @parent
    {!! csrf_field() !!}
    <script src="{{ asset('js/admin/jquery.deleteResource.js') }}"></script>
@stop
