@extends('uranus::layout')
@section('content')
        <!-- Page -->
<div class="page animsition">
    <div class="page-header">
        <h1 class="page-title">{{request('q')? 'Αποτελεσματα Αναζήτησης παραστάσεων για \''.request('q').'\'':'Παραστάσεις'}}</h1>
        <div class="page-header-actions">
            @admincan('manage_plays')
            <a href="{!! route('uranus.plays.create') !!}">
                <button type="button" class="btn btn-floating btn-danger">
                    <i class="icon wb-plus" aria-hidden="true"></i></button>
            </a>
            @endadmincan
        </div>
    </div>
    <div class="page-content">
        <!-- Panel Basic -->
        <div class="panel">
            <div class="panel-body">
                {{--Search bar--}}
                <div>
                    <form method="get" class="search_form pull-left" action="{{ route('uranus.plays.index') }}">
                        <input type="text" id="search_query" name="q" value="{{ request('q') }}">
                        <button type="submit">Αναζήτηση</button>
                    </form>
                    <span class="pull-right">
                        <a href="{!! route('uranus.plays.index', ["t" => "notTranslated"]) !!}">{!! $notTranslated !!} / {!! $allPlays !!} plays not translated</a>
                    </span>
                </div>

                <table class="table table-hover dataTable table-striped width-full" data-plugin=" ">
                    <thead>
                        <tr>
                            <th>{!! \App\Components\LinkTo::sortableRoute('uranus.plays.index','Δημ/νη','published') !!} - {!! \App\Components\LinkTo::sortableRoute('uranus.plays.index','ID','id') !!}</th>
                            <th>{!! \App\Components\LinkTo::sortableRoute('uranus.plays.index','Τίτλος','title') !!} - {!! \App\Components\LinkTo::sortableRoute('uranus.plays.index','Έτος','year') !!}</th>
                            <th>{!! \App\Components\LinkTo::sortableRoute('uranus.plays.index','Θέατρο','theatre_id') !!}</th>
                            <th>{!! \App\Components\LinkTo::sortableRoute('uranus.plays.index','Έναρξη','start_date') !!} - {!! \App\Components\LinkTo::sortableRoute('uranus.plays.index','Λήξη','end_date') !!}</th>
                            <th>{!! \App\Components\LinkTo::sortableRoute('uranus.plays.index','Δημιουργία','created_at') !!}</th>
                            <th>{!! \App\Components\LinkTo::sortableRoute('uranus.plays.index','Σημειώσεις','notes') !!}</th>
                            <th></th>
                            <th></th>
                            <th></th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($plays as $play)
                            <tr>
                                <td>@if($play->published)
                                        <div class="text-center">
                                            <i class="icon wb-eye green-600" aria-hidden="true" title="Δημοσιευμένη"></i>
                                        </div>
                                    @else
                                        <div class="text-center">
                                            <i class="icon wb-eye-close red-600" aria-hidden="true" title="Αδημοσίευτη"></i>
                                        </div>
                                    @endif
                                    <a target="_blank" href="{{ route('uranus.plays.edit', $play->id) }}">{{ $play->id }}</a>
                                </td>
                                <td class="showPopoverInfo" data-url="{!! route('uranus.plays.popoverInfo') !!}" data-id="{!! $play->id !!}">
                                    <a target="_blank" href="{{ route('uranus.plays.edit', $play->id) }}">{{ $play->title }} - {{ $play->year }}</a>
                                </td>
                                @if($play->theatre)
                                    <td><a target="_blank" href="{{ route('uranus.theatres.edit', $play->theatre->id) }}">{{ $play->theatre->name }}</a></td>
                                @else
                                    <td></td>
                                @endif
                                <td>{!! $play->start_date !!} - {!! $play->end_date !!}</td>
                                <td>{!! $play->created_at !!}</td>
                                <td>{!! $play->notes !!}</td>
                                @admincan('manage_plays')
                                <td>
                                    <a target="_blank" href="{!! route('uranus.plays.create.replica', $play->id) !!}">
                                        <button type="button" class="btn btn-icon btn-warning btn-outline" title="{!! trans('uranus::plays.edit_replicate_tooltip') !!}">
                                            <i class="icon wb-copy" aria-hidden="true"></i></button>
                                    </a>
                                </td>
                                <td>
                                    <a target="_blank" href="{!! route('uranus.plays.edit', $play->id) !!}">
                                        <button type="button" class="btn btn-icon btn-default btn-outline" title="Επεξεργασία">
                                            <i class="icon wb-pencil" aria-hidden="true"></i></button>
                                    </a>
                                </td>
                                <td>
                                    <a target="_blank" href="{!! route('uranus.play.timetable.show', $play->id) !!}">
                                        <button type="button" class="btn btn-icon btn-default btn-outline" title="Ημερολόγιο">
                                            <i class="icon wb-calendar" aria-hidden="true"></i></button>
                                    </a>
                                </td>
                                @endadmincan
                                @admincan('delete_plays')
                                <td>
                                    <a data-url="{!! route('uranus.plays.destroy', $play->id) !!}" href="" class="deleteResource">
                                        <button type="button" class="btn btn-icon btn-danger btn-outline" title="Διαγραφή">
                                            <i class="fa fa-times" aria-hidden="true"></i></button>
                                    </a>
                                </td>
                                @endadmincan
                            </tr>
                        @empty
                            <p>Δε βρέθηκαν έργα</p>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>
        <!-- End Panel Basic -->
        {!! $plays->render() !!}
    </div>
</div>
<!-- End Page -->
@stop

@section('scripts')
    @parent
    {!! csrf_field() !!}
    <script src="{{ asset('js/admin/jquery.deleteResource.js') }}"></script>
    <script src="{{ asset('js/admin/jquery.mouseoverPopover.js') }}"></script>
@stop
