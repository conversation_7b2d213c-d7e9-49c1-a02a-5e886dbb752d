@extends('uranus::layout')
@section('head')
    <link type="text/css" rel="stylesheet"
            href="https://ajax.googleapis.com/ajax/libs/jqueryui/1.10.2/themes/smoothness/jquery-ui.min.css"
            media="screen"/>
    <link rel="stylesheet" href="{{asset('assets/vendor/bootstrap-datepicker/bootstrap-datepicker.css')}}">
    @stop

    @section('content')
            <!-- Page -->
    <div class="page animsition">
        <div class="page-header">
            <h1 class="page-title">{{$play->title}}</h1>
            <h4>
                <a target="_blank" href="{{ route('uranus.plays.translatable.edit', $play->id) }}">
                    {{ $play->hasTranslation('en') ? $play->translate('en')->title . ' (en)' : "No (en) title" }}
                </a>
            </h4>
            <div class="page-header-actions">

                <a href="{{ route('uranus.play.revisions',$play->id) }}">
                    <button type="button" class="btn btn-floating btn-default" title="{!! trans('uranus::common.revisions_tooltip') !!}">
                        <i class="fa fa-lg fa-history blue-600" style="font-size: 24px"></i></button>
                </a>
                <a target="_blank" href="{!! route('plays.show', ['slug' => $play->slug, 'ref_' => \App\Components\UrlReferrals::getReferral('play_details', 'uranus_play_edit')]) !!}">
                    <button type="button" class="btn btn-floating btn-primary" title="{!! trans('uranus::common.preview_tooltip') !!}">
                        <i class="icon wb-eye" aria-hidden="true"></i></button>
                </a>
                <a href="{!! route('uranus.plays.create.replica', $play->id) !!}">
                    <button type="button" class="btn btn-floating btn-warning" title="{!! trans('uranus::plays.edit_replicate_tooltip') !!}"><i class="icon wb-copy"
                                aria-hidden="true"></i></button>
                </a>
                <a href="{!! route('uranus.plays.videos.index', $play->id) !!}">
                    <button type="button" class="btn btn-floating btn-success" title="{!! trans('uranus::plays.videos') !!}"><i class="icon wb-camera"
                                aria-hidden="true"></i></button>
                </a>
                <a href="{!! route('uranus.images.moderation.index', ['play_id' => $play->id]) !!}">
                    <button type="button" class="btn btn-floating btn-success" title="{!! trans('uranus::plays.images') !!}"><i class="icon wb-image"
                                aria-hidden="true"></i></button>
                </a>
                <a href="{!! route('uranus.plays.characters.edit', $play->id) !!}">
                    <button type="button" class="btn btn-floating btn-success" title="{!! trans('uranus::plays.characters') !!}"><i class="icon wb-user"
                                aria-hidden="true"></i></button>
                </a>
                <a href="{!! route('uranus.criticReviews.index', $play->id) !!}">
                    <button type="button" class="btn btn-floating btn-success" title="{!! trans('uranus::plays.criticReviews_list_tooltip') !!}"><i class="icon wb-star"
                                aria-hidden="true"></i></button>
                </a>
                <a href="{!! route('uranus.play.timetable.show', $play->id) !!}">
                    <button type="button" class="btn btn-floating btn-success" title="{!! trans('uranus::plays.edit_calendar') !!}"><i class="icon wb-calendar"
                                aria-hidden="true"></i></button>
                </a>
                <a href="{!! route('uranus.plays.translatable.edit', $play->id) !!}">
                    <button type="button" class="btn btn-floating btn-warning" title="{!! trans('uranus::plays.edit_translatable') !!}"><i class="icon wb-globe"
                                aria-hidden="true"></i></button>
                </a>
                <a href="{!! route('uranus.plays.parent.edit', $play->id) !!}">
                    <button type="button" class="btn btn-floating btn-info" title="{!! trans('uranus::plays.edit_parent') !!}"><i class="icon wb-users"
                                aria-hidden="true"></i></button>
                </a>
                <a href="{!! route('uranus.plays.create') !!}">
                    <button type="button" class="btn btn-floating btn-danger" title="{!! trans('uranus::plays.create_play_tooltip') !!}"><i class="icon wb-plus"
                                aria-hidden="true"></i></button>
                </a>
            </div>
        </div>
        <div class="page-content">
            <div class="container-fluid">
                @include('uranus::messages.successfulSave')
                @include('uranus::errors.genericForm')
                <div class="row">
                    <div class="col-sm-6">
                        <!-- Panel Static Lables -->
                        <div class="panel">
                            <div class="panel-heading">
                                <h4 class="panel-title">Γενικές πληροφορίες παράστασης</h4>
                            </div>
                            <div class="panel-body container-fluid">
                                <p class="text-muted" style="margin-bottom: 15px;">
                                    <strong>Slug:</strong> <code>{{ $play->slug }}</code>
                                </p>
                                <form action="{{route('uranus.plays.update', $play->id)}}" method="post" enctype="multipart/form-data">
                                    {!! csrf_field() !!}
                                    {!! method_field('put') !!}
                                    @include('uranus::plays._playForm', ['submitButtonText'=>'Αποθήκευση'])
                                </form>
                            </div>
                        </div>
                        <!-- End Panel Static Lables -->
                    </div>
                    <div class="col-sm-6">
                        <!-- Panel Floating Lables -->
                        <div class="panel">
                            @include('uranus::images.manage')
                        </div>

                        @include('uranus::_partials._personCreator')

                        <div class="panel">
                            <div class="panel-heading">
                                <h3 class="panel-title">Συντελεστές παράστασης</h3>
                            </div>
                            <div class="panel-body container-fluid">
                                @include('uranus::plays._playRolesSelects', ['roles' => config('roles')->where('for_plays', true)->sortBy('sort_order')])
                            </div>
                        </div>
                    </div>
                    <!-- End Panel Floating Lables -->
                </div>
            </div>
        </div>
    </div>
    <!-- End Page -->
@stop
@section('footer')
    @parent
    <script type="text/javascript" src="https://ajax.googleapis.com/ajax/libs/jqueryui/1.10.2/jquery-ui.min.js"
            charset="UTF-8"></script>
    <script src="{{asset('assets/vendor/bootstrap-datepicker/bootstrap-datepicker.js')}}"></script>
    <script src="{{asset('assets/js/components/bootstrap-datepicker.js')}}"></script>
@stop
