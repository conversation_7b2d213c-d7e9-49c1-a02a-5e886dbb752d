# People Untranslated Link Implementation

## Overview
This document outlines the implementation of an untranslated people link in the Uranus admin interface, following the same pattern as the TV shows implementation.

## What Was Implemented

### 1. **Added `notTranslatedIn` Scope to Person Model**
**File**: `app/Models/Traits/Scopes/PersonScopes.php`

Added the `scopeNotTranslatedIn` method to enable filtering people who don't have translations in a specific locale:

```php
/**
 * Scope to get people that are not translated in a specific locale
 *
 * @param Builder $query
 * @param string $locale
 * @return Builder
 */
public function scopeNotTranslatedIn(Builder $query, $locale)
{
    return $query->whereDoesntHave('translations', function ($translationQuery) use ($locale) {
        $translationQuery->where('locale', $locale);
    });
}
```

### 2. **Updated PeopleController**
**File**: `packages/Uranus/app/Http/Controllers/PeopleController.php`

Enhanced the `index` method to:
- Count total people
- Count untranslated people
- Pass these counts to the view

```php
// Get all people
$allPeople = Person::count();

// Get not translated people count
$notTranslated = Person::notTranslatedIn('en')->count();

return view('uranus::people.index', compact('people', 'allPeople', 'notTranslated'));
```

**Note**: The controller already had the logic to filter by `notTranslated` parameter (lines 74-77), so no additional filtering logic was needed.

### 3. **Updated People Index View**
**File**: `packages/Uranus/resources/views/people/index.blade.php`

Added the untranslated count and link in the search bar area:

```php
@if($notTranslated > 0)
    <span class="pull-right">
    <a href="{!! route('uranus.people.index', ["t" => "notTranslated"]) !!}">{!! $notTranslated !!} / {!! $allPeople !!} people not translated</a>
</span>
@endif
```

### 4. **Created Console Command**
**File**: `app/Console/Commands/FindMissingPeopleTranslations.php`

Created a console command to analyze people translation coverage:

```bash
php artisan people-translations:find-missing --locale=en --export
```

**Features**:
- Shows total people count
- Shows translated vs untranslated counts
- Calculates translation coverage percentage
- Displays first 10 untranslated people
- Optional CSV export functionality

## Functionality

### **Admin Interface**
1. **People Index Page**: Shows "X / Y people not translated" link when untranslated people exist
2. **Filtering**: Clicking the link filters to show only untranslated people
3. **URL Parameter**: Uses `?t=notTranslated` parameter for filtering

### **Console Command**
```bash
# Basic usage
php artisan people-translations:find-missing

# Specify locale
php artisan people-translations:find-missing --locale=en

# Export to CSV
php artisan people-translations:find-missing --locale=en --export
```

**Sample Output**:
```
Finding people missing en translations...
Results:
- Total People: 1,234
- Translated (en): 456
- Missing translations: 778
- Translation coverage: 36.95%

First 10 people missing en translations:
+----+------------------+---------------------+
| ID | Full Name (Greek)| Created At          |
+----+------------------+---------------------+
| 1  | Γιάννης Παπάς    | 2023-01-15 10:30:00 |
| 2  | Μαρία Κώστα      | 2023-01-16 14:20:00 |
+----+------------------+---------------------+
```

## Pattern Consistency

This implementation follows the **exact same pattern** as the TV shows implementation:

### **TV Shows Pattern** (Reference)
- **Scope**: `TvShow::notTranslatedIn('en')`
- **Controller**: Counts and passes `$allTvShows`, `$notTranslated`
- **View**: Shows "X / Y tv shows not translated" link
- **URL**: `?t=notTranslated` parameter
- **Command**: `FindMissingTvShowTranslations`

### **People Pattern** (Implemented)
- **Scope**: `Person::notTranslatedIn('en')`
- **Controller**: Counts and passes `$allPeople`, `$notTranslated`
- **View**: Shows "X / Y people not translated" link
- **URL**: `?t=notTranslated` parameter
- **Command**: `FindMissingPeopleTranslations`

## Database Schema

The implementation leverages the existing translation infrastructure:

- **Main Table**: `people`
- **Translation Table**: `people_translations`
- **Translation Fields**: `first_name`, `last_name`, `bio`, `auto_biography_text`, `user_bio`, `living_place`, `birth_place`, `death_place`
- **Locale Field**: `locale` (values: 'el', 'en')

## Benefits

1. **Visibility**: Admins can quickly see how many people need translation
2. **Efficiency**: Easy filtering to work on untranslated content
3. **Tracking**: Console command provides detailed analytics
4. **Consistency**: Follows established patterns from TV shows
5. **Scalability**: Works with any locale, not just English

## Usage Examples

### **Admin Workflow**
1. Visit `/uranus/people` in admin
2. See "123 / 456 people not translated" link
3. Click link to filter untranslated people
4. Work on translating people profiles
5. Use console command to track progress

### **Translation Management**
```bash
# Check current status
php artisan people-translations:find-missing

# Export list for translation team
php artisan people-translations:find-missing --export

# Check progress after translation work
php artisan people-translations:find-missing
```

## Future Enhancements

1. **Bulk Translation**: Add bulk translation interface
2. **Priority Sorting**: Sort by importance (e.g., number of plays/movies)
3. **Translation Status**: Track partial vs complete translations
4. **Automated Translation**: Integration with translation services
5. **Progress Tracking**: Historical translation progress charts

## Testing

The implementation can be tested by:

1. **Accessing the admin interface**: `/uranus/people`
2. **Verifying the link appears** when untranslated people exist
3. **Clicking the link** to filter untranslated people
4. **Running the console command** to verify counts match

**Note**: Database connection required for full testing in Docker environment.

## Conclusion

The people untranslated link implementation provides the same functionality as TV shows, enabling efficient management of translation work for people/professionals in the unstage platform. The implementation maintains consistency with existing patterns while providing powerful tools for translation management.
